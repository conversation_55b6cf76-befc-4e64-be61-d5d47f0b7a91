"""
File Manager
Handles file operations for Obsidian vault
"""

import os
from pathlib import Path
from typing import List, Optional, Dict
from .utils import logger


class FileManager:
    """Manages file operations for Obsidian vault"""
    
    def __init__(self, obsidian_path: str):
        """
        Initialize the file manager
        
        Args:
            obsidian_path: Path to Obsidian vault
        """
        self.obsidian_path = Path(obsidian_path)
        self.supported_media = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg', '.pdf', '.mp4', '.mp3', '.wav'}
    
    def read_allowed_list(self) -> Optional[List[str]]:
        """Read allowed.list file and return list of allowed filenames"""
        allowed_list_path = Path("allowed.list")
        
        if not allowed_list_path.exists():
            return None
            
        try:
            with open(allowed_list_path, 'r', encoding='utf-8') as f:
                allowed_files = []
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):  # Skip empty lines and comments
                        allowed_files.append(line)
                
                # If file exists but contains no valid entries (only comments/empty lines),
                # treat it as if no filter should be applied
                if len(allowed_files) == 0:
                    logger.info("Found allowed.list but it's empty (only comments) - syncing all files")
                    return None
                
                logger.info(f"Found allowed.list with {len(allowed_files)} files")
                return allowed_files
                
        except Exception as e:
            logger.error(f"Error reading allowed.list: {e}")
            return None

    def get_obsidian_notes(self) -> List[Path]:
        """Get all markdown files from Obsidian vault"""
        notes = []
        
        if not self.obsidian_path.exists():
            logger.error(f"Obsidian path does not exist: {self.obsidian_path}")
            return notes
        
        # Check if allowed.list exists and read it
        allowed_files = self.read_allowed_list()
        
        # Find all .md files
        for md_file in self.obsidian_path.rglob("*.md"):
            # Skip hidden folders and files
            if any(part.startswith('.') for part in md_file.parts):
                continue
            
            # If allowed.list exists and is not empty, filter files
            if allowed_files is not None:
                # Check if the filename (with or without .md extension) is in allowed list
                filename_with_ext = md_file.name
                filename_without_ext = md_file.stem
                
                if (filename_with_ext not in allowed_files and 
                    filename_without_ext not in allowed_files):
                    logger.debug(f"Skipping {filename_with_ext} - not in allowed.list")
                    continue
                else:
                    logger.info(f"Including {filename_with_ext} - found in allowed.list")
            
            notes.append(md_file)
        
        if allowed_files is not None:
            logger.info(f"Found {len(notes)} markdown files in Obsidian vault (filtered by allowed.list)")
        else:
            logger.info(f"Found {len(notes)} markdown files in Obsidian vault (no allowed.list filter)")
            
        return notes
    
    def organize_notes_by_folder(self, notes: List[Path]) -> Dict[str, List[Path]]:
        """Organize notes by their folder structure"""
        organized = {}
        
        for note in notes:
            # Get relative path from obsidian root
            rel_path = note.relative_to(self.obsidian_path)
            
            if len(rel_path.parts) == 1:
                # Root level file
                folder_name = "Root"
            else:
                # Use parent folder name
                folder_name = str(rel_path.parent)
            
            if folder_name not in organized:
                organized[folder_name] = []
            organized[folder_name].append(note)
        
        return organized
    
    def find_media_file(self, base_path: Path, media_name: str) -> Optional[Path]:
        """Find media file in common attachment folders"""
        search_folders = [
            base_path,
            base_path / "attachments",
            base_path / "assets", 
            base_path / "media",
            self.obsidian_path / "attachments",
            self.obsidian_path / "assets",
            self.obsidian_path / "media"
        ]
        
        for folder in search_folders:
            if folder.exists():
                media_path = folder / media_name
                if media_path.exists():
                    return media_path
        
        return None
