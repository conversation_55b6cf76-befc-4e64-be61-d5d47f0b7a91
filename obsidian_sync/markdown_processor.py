"""
Markdown Processor
Converts Obsidian markdown to Google Docs API requests
"""

import re
from pathlib import Path
from typing import List, Dict, Tuple
import markdown
from bs4 import BeautifulSoup
from .utils import logger, grapheme_len, clean_whitespace_lines
from .hierarchy_list_processor import HierarchyListProcessor


class MarkdownProcessor:
    """Processes Obsidian markdown and converts to Google Docs requests"""
    
    def __init__(self, file_manager):
        """
        Initialize the markdown processor

        Args:
            file_manager: FileManager instance for finding media files
        """
        self.file_manager = file_manager
        self.math_expressions = {}  # Store math expressions with their placeholders
        self.link_info = {}  # Store link information with placeholders
        self.hierarchy_processor = HierarchyListProcessor(self)  # Enhanced list processor
    
    def convert_markdown_to_docs_requests(self, markdown_content: str, note_path: Path) -> List[Dict]:
        """Convert Obsidian markdown to Google Docs API requests"""
        try:
            # Clean up whitespace-only lines before processing
            cleaned_content = clean_whitespace_lines(markdown_content)

            # Pre-process math expressions before other processing
            processed_content = self.preprocess_math_expressions(cleaned_content)
            
            # Pre-process links to extract link information
            processed_content, link_info = self.preprocess_links(processed_content)

            requests = []
            # Start at index 1 - Google Docs documents start with a newline at index 0
            # and content should be inserted starting at index 1
            index = 1

            # Process the markdown line by line to handle lists correctly
            requests, index = self.process_markdown_lines(processed_content, index)

            # Handle Obsidian-specific syntax (links, etc. - images are handled separately)
            requests = self.process_obsidian_links(requests, markdown_content)

            return requests

        except Exception as e:
            logger.error(f"Error converting markdown: {e}")
            return []

    def process_markdown_lines(self, markdown_content: str, start_index: int) -> Tuple[List[Dict], int]:
        """Process markdown content line by line to handle lists correctly"""
        requests = []
        current_index = start_index
        
        lines = markdown_content.split('\n')
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            # Check if this line starts a list
            if self._is_list_item(line):
                # Process the entire list block
                list_end = self._find_list_end(lines, i)
                list_lines = lines[i:list_end]
                list_requests, list_length = self._process_list_lines(list_lines, current_index)
                requests.extend(list_requests)
                current_index += list_length
                i = list_end
            elif line.strip().startswith('#'):
                # Handle headers
                header_requests, header_length = self._process_header_line(line, current_index)
                requests.extend(header_requests)
                current_index += header_length
                i += 1
            elif line.strip():
                # Handle regular paragraphs
                para_requests, para_length = self._process_paragraph_line(line, current_index)
                requests.extend(para_requests)
                current_index += para_length
                i += 1
            else:
                # Empty line
                i += 1
        
        return requests, current_index - start_index
    
    def _process_list_lines(self, lines: List[str], start_index: int) -> Tuple[List[Dict], int]:
        """Process a block of list lines using enhanced hierarchy processor"""
        try:
            # Use the enhanced hierarchy list processor
            return self.hierarchy_processor.process_hierarchy_lists(lines, start_index)
        except Exception as e:
            logger.error(f"Error processing hierarchy lists: {e}")
            # Fallback to basic processing
            return self._process_list_lines_basic(lines, start_index)
    
    def _process_list_lines_basic(self, lines: List[str], start_index: int) -> Tuple[List[Dict], int]:
        """Basic list processing as fallback (legacy method)"""
        requests = []
        current_index = start_index
        
        # Parse the list structure with improved hierarchy detection
        items = self._parse_list_items_hierarchical(lines)
        
        # Track nesting levels for proper Google Docs list formatting
        nesting_stack = []
        
        # Convert each item to Google Docs requests
        for item in items:
            indent_level = item['indent_level']
            list_type = item['list_type']
            content = item['content']
            nesting_level = item['nesting_level']
            
            if content.strip():
                # Process math expressions in the text
                processed_text, _ = self._process_math_in_text(content, [])
                text_with_newline = processed_text + '\n'
                text_length = grapheme_len(text_with_newline)
                
                # Insert the text
                requests.append({
                    'insertText': {
                        'location': {'index': current_index},
                        'text': text_with_newline
                    }
                })
                
                # Apply list formatting with proper hierarchy support
                is_ordered = list_type == 'ol'
                bullet_preset = self._get_bullet_preset_for_level(is_ordered, nesting_level)
                
                requests.append({
                    'createParagraphBullets': {
                        'range': {
                            'startIndex': current_index,
                            'endIndex': current_index + text_length - 1  # Exclude the newline
                        },
                        'bulletPreset': bullet_preset
                    }
                })
                
                # Apply proper nesting level using Google Docs list nesting
                if nesting_level > 0:
                    requests.append({
                        'updateParagraphStyle': {
                            'range': {
                                'startIndex': current_index,
                                'endIndex': current_index + text_length - 1
                            },
                            'paragraphStyle': {
                                'indentFirstLine': {
                                    'magnitude': nesting_level * 36,  # 36 points per level for better spacing
                                    'unit': 'PT'
                                },
                                'indentStart': {
                                    'magnitude': nesting_level * 36,
                                    'unit': 'PT'
                                },
                                'direction': 'LEFT_TO_RIGHT'
                            },
                            'fields': 'indentFirstLine,indentStart,direction'
                        }
                    })
                
                current_index += text_length
        
        return requests, current_index - start_index
    
    def _process_header_line(self, line: str, start_index: int) -> Tuple[List[Dict], int]:
        """Process a header line"""
        requests = []
        
        # Count the number of # to determine header level
        level = 0
        for char in line:
            if char == '#':
                level += 1
            else:
                break
        
        # Extract header text
        text = line[level:].strip() + '\n'
        text_length = grapheme_len(text)
        
        # Insert the text
        requests.append({
            'insertText': {
                'location': {'index': start_index},
                'text': text
            }
        })
        
        # Apply heading style
        requests.append({
            'updateParagraphStyle': {
                'range': {
                    'startIndex': start_index,
                    'endIndex': start_index + text_length - 1
                },
                'paragraphStyle': {
                    'namedStyleType': f'HEADING_{level}'
                },
                'fields': 'namedStyleType'
            }
        })
        
        return requests, text_length
    
    def _process_paragraph_line(self, line: str, start_index: int) -> Tuple[List[Dict], int]:
        """Process a regular paragraph line"""
        requests = []
        
        # Process math expressions in the text
        processed_text, _ = self._process_math_in_text(line, [])
        text_with_newline = processed_text + '\n'
        text_length = grapheme_len(text_with_newline)
        
        # Insert the text
        requests.append({
            'insertText': {
                'location': {'index': start_index},
                'text': text_with_newline
            }
        })
        
        return requests, text_length





    def preprocess_indented_lists(self, markdown_content: str) -> str:
        """Preprocess indented lists to create proper nested HTML structure"""
        lines = markdown_content.split('\n')
        processed_lines = []
        
        i = 0
        while i < len(lines):
            line = lines[i]
            
            # Check if this line starts a list
            if self._is_list_item(line):
                # Process the entire list block
                list_end = self._find_list_end(lines, i)
                list_lines = lines[i:list_end]
                processed_list = self._process_list_block(list_lines)
                processed_lines.extend(processed_list)
                i = list_end
            else:
                processed_lines.append(line)
                i += 1
        
        return '\n'.join(processed_lines)
    
    def _is_list_item(self, line: str) -> bool:
        """Check if a line is a list item"""
        stripped = line.lstrip()
        return (stripped.startswith('- ') or 
                stripped.startswith('* ') or 
                stripped.startswith('+ ') or
                (len(stripped) > 0 and stripped[0].isdigit() and '. ' in stripped[:10]))
    
    def _find_list_end(self, lines: List[str], start: int) -> int:
        """Find the end of a list block"""
        i = start + 1
        while i < len(lines):
            line = lines[i]
            
            # Empty line - check if next line continues the list
            if not line.strip():
                # Look ahead to see if we have more list items
                j = i + 1
                while j < len(lines) and not lines[j].strip():
                    j += 1
                
                if j < len(lines) and self._is_list_item(lines[j]):
                    i = j
                    continue
                else:
                    # End of list
                    break
            elif self._is_list_item(line):
                i += 1
            elif line.startswith('  ') or line.startswith('\t'):
                # Continuation of previous list item or nested content
                i += 1
            else:
                # Not a list item and not indented - end of list
                break
        
        return i
    
    def _process_list_block(self, lines: List[str]) -> List[str]:
        """Process a block of list lines to create proper nested structure"""
        if not lines:
            return []
        
        # Parse the list structure
        items = self._parse_list_items(lines)
        
        # Convert to HTML-like structure that markdown can understand
        return self._convert_to_nested_markdown(items)
    
    def _parse_list_items_hierarchical(self, lines: List[str]) -> List[Dict]:
        """Parse list lines into a hierarchical structured format with proper nesting levels"""
        items = []
        i = 0
        nesting_stack = []  # Stack to track nesting levels
        
        while i < len(lines):
            line = lines[i]
            if not self._is_list_item(line):
                i += 1
                continue
            
            # Calculate indentation level (support both spaces and tabs)
            indent_level = self._calculate_indent_level(line)
            
            # Determine list type and extract content
            stripped = line.lstrip()
            if stripped.startswith(('- ', '* ', '+ ')):
                list_type = 'ul'
                content = stripped[2:].strip()
            else:
                # Ordered list
                list_type = 'ol'
                # Find the '. ' to extract content
                dot_pos = stripped.find('. ')
                if dot_pos != -1:
                    content = stripped[dot_pos + 2:].strip()
                else:
                    content = stripped
            
            # Collect continuation lines (multi-line list items)
            i += 1
            while i < len(lines) and not self._is_list_item(lines[i]):
                continuation = lines[i]
                if continuation.strip():
                    # Handle continuation lines properly
                    continuation_indent = self._calculate_indent_level(continuation)
                    if continuation_indent > indent_level:
                        # This is a continuation of the current list item
                        content += ' ' + continuation.strip()
                    else:
                        # This might be the end of the list
                        break
                i += 1
            
            # Calculate proper nesting level based on indentation hierarchy
            nesting_level = self._calculate_nesting_level(indent_level, nesting_stack)
            
            # Update nesting stack
            while nesting_stack and nesting_stack[-1]['indent'] >= indent_level:
                nesting_stack.pop()
            
            nesting_stack.append({
                'indent': indent_level,
                'type': list_type,
                'nesting_level': nesting_level
            })
            
            items.append({
                'indent_level': indent_level,
                'list_type': list_type,
                'content': content,
                'nesting_level': nesting_level
            })
        
        return items
    
    def _calculate_indent_level(self, line: str) -> int:
        """Calculate indentation level supporting both spaces and tabs"""
        indent = 0
        for char in line:
            if char == ' ':
                indent += 1
            elif char == '\t':
                indent += 4  # Treat tab as 4 spaces
            else:
                break
        return indent
    
    def _calculate_nesting_level(self, current_indent: int, nesting_stack: List[Dict]) -> int:
        """Calculate the proper nesting level based on indentation hierarchy"""
        if not nesting_stack:
            return 0
        
        # Find the deepest parent that has less indentation than current
        for i in range(len(nesting_stack) - 1, -1, -1):
            if nesting_stack[i]['indent'] < current_indent:
                return nesting_stack[i]['nesting_level'] + 1
        
        return 0
    
    def _get_bullet_preset_for_level(self, is_ordered: bool, nesting_level: int) -> str:
        """Get appropriate bullet preset based on list type and nesting level"""
        if is_ordered:
            # Ordered list presets that work well with nesting
            presets = [
                'NUMBERED_DECIMAL_ALPHA_ROMAN',
                'NUMBERED_DECIMAL_ALPHA_ROMAN_PARENS',
                'NUMBERED_DECIMAL_NESTED'
            ]
            return presets[min(nesting_level, len(presets) - 1)]
        else:
            # Unordered list presets with different bullet styles per level
            presets = [
                'BULLET_DISC_CIRCLE_SQUARE',
                'BULLET_DIAMONDX_ARROW3D_SQUARE',
                'BULLET_CHECKBOX'
            ]
            return presets[min(nesting_level, len(presets) - 1)]

    def _parse_list_items(self, lines: List[str]) -> List[Dict]:
        """Parse list lines into a structured format (legacy method for backward compatibility)"""
        items = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            if not self._is_list_item(line):
                i += 1
                continue
            
            # Determine indentation level
            indent_level = len(line) - len(line.lstrip())
            
            # Determine list type and extract content
            stripped = line.lstrip()
            if stripped.startswith(('- ', '* ', '+ ')):
                list_type = 'ul'
                content = stripped[2:].strip()
            else:
                # Ordered list
                list_type = 'ol'
                # Find the '. ' to extract content
                dot_pos = stripped.find('. ')
                if dot_pos != -1:
                    content = stripped[dot_pos + 2:].strip()
                else:
                    content = stripped
            
            # Collect continuation lines
            i += 1
            while i < len(lines) and not self._is_list_item(lines[i]):
                continuation = lines[i]
                if continuation.strip():
                    # Remove the list item's indentation from continuation
                    if len(continuation) > indent_level + 2:  # +2 for the "- " or "1. "
                        content += ' ' + continuation[indent_level + 2:].strip()
                    else:
                        content += ' ' + continuation.strip()
                i += 1
            
            items.append({
                'indent_level': indent_level,
                'list_type': list_type,
                'content': content
            })
        
        return items
    
    def _convert_to_nested_markdown(self, items: List[Dict]) -> List[str]:
        """Convert parsed items back to properly nested markdown"""
        if not items:
            return []
        
        result = []
        stack = []  # Stack to track nesting levels
        
        for item in items:
            indent_level = item['indent_level']
            list_type = item['list_type']
            content = item['content']
            
            # Adjust stack based on current indentation
            while stack and stack[-1]['indent_level'] >= indent_level:
                stack.pop()
            
            # Create proper markdown indentation
            if not stack:
                # Top level item
                if list_type == 'ul':
                    result.append(f"- {content}")
                else:
                    result.append(f"1. {content}")
            else:
                # Nested item - create proper indentation
                parent_level = stack[-1]['indent_level'] if stack else 0
                relative_indent = (indent_level - parent_level) // 2  # Assuming 2 spaces per level
                markdown_indent = "  " * relative_indent
                
                if list_type == 'ul':
                    result.append(f"{markdown_indent}- {content}")
                else:
                    result.append(f"{markdown_indent}1. {content}")
            
            # Add to stack
            stack.append({
                'indent_level': indent_level,
                'list_type': list_type
            })
        
        return result
    
    def _is_list_item(self, line: str) -> bool:
        """Check if a line is a list item"""
        stripped = line.lstrip()
        return (stripped.startswith('- ') or 
                stripped.startswith('* ') or 
                stripped.startswith('+ ') or
                (len(stripped) > 0 and stripped[0].isdigit() and '. ' in stripped[:10]))
    
    def _find_list_end(self, lines: List[str], start: int) -> int:
        """Find the end of a list block"""
        i = start + 1
        while i < len(lines):
            line = lines[i]
            
            # Empty line - check if next line continues the list
            if not line.strip():
                # Look ahead to see if we have more list items
                j = i + 1
                while j < len(lines) and not lines[j].strip():
                    j += 1
                
                if j < len(lines) and self._is_list_item(lines[j]):
                    i = j
                    continue
                else:
                    # End of list
                    break
            elif self._is_list_item(line):
                i += 1
            elif line.startswith('  ') or line.startswith('\t'):
                # Continuation of previous list item or nested content
                i += 1
            else:
                # Not a list item and not indented - end of list
                break
        
        return i

    def process_list_with_formatting(self, list_element, start_index: int) -> Tuple[List[Dict], int]:
        """Process lists with proper Google Docs list formatting including nested lists"""
        requests = []
        current_index = start_index
        
        # Process the list recursively starting at nesting level 0
        list_requests, list_length = self._process_list_at_level(list_element, current_index, 0)
        requests.extend(list_requests)
        current_index += list_length
        
        return requests, current_index - start_index
    
    def _process_list_at_level(self, list_element, start_index: int, nesting_level: int) -> Tuple[List[Dict], int]:
        """Process a list at a specific nesting level"""
        requests = []
        current_index = start_index
        
        # Determine if this is an ordered or unordered list
        is_ordered = list_element.name == 'ol'
        
        # Process each list item
        for li in list_element.find_all('li', recursive=False):
            # Get the direct text content of this list item (excluding nested lists)
            li_text = self._get_direct_text_content(li)
            
            if li_text.strip():
                # Process math expressions in the text
                processed_text, _ = self._process_math_in_text(li_text, [])
                text_with_newline = processed_text + '\n'
                text_length = grapheme_len(text_with_newline)
                
                # Insert the text
                requests.append({
                    'insertText': {
                        'location': {'index': current_index},
                        'text': text_with_newline
                    }
                })
                
                # Apply list formatting
                requests.append({
                    'createParagraphBullets': {
                        'range': {
                            'startIndex': current_index,
                            'endIndex': current_index + text_length - 1  # Exclude the newline
                        },
                        'bulletPreset': 'NUMBERED_DECIMAL_ALPHA_ROMAN' if is_ordered else 'BULLET_DISC_CIRCLE_SQUARE'
                    }
                })
                
                # Apply nesting level indentation if needed
                if nesting_level > 0:
                    requests.append({
                        'updateParagraphStyle': {
                            'range': {
                                'startIndex': current_index,
                                'endIndex': current_index + text_length - 1
                            },
                            'paragraphStyle': {
                                'indentFirstLine': {
                                    'magnitude': nesting_level * 18,  # 18 points per level
                                    'unit': 'PT'
                                },
                                'indentStart': {
                                    'magnitude': nesting_level * 18,
                                    'unit': 'PT'
                                }
                            },
                            'fields': 'indentFirstLine,indentStart'
                        }
                    })
                
                current_index += text_length
            
            # Process nested lists within this list item
            for nested_list in li.find_all(['ul', 'ol'], recursive=False):
                nested_requests, nested_length = self._process_list_at_level(nested_list, current_index, nesting_level + 1)
                requests.extend(nested_requests)
                current_index += nested_length
        
        return requests, current_index - start_index
    
    def _get_direct_text_content(self, li_element) -> str:
        """Get the direct text content of a list item, excluding nested lists"""
        # Get all direct text content, excluding nested lists
        text_parts = []
        
        for content in li_element.contents:
            if hasattr(content, 'name'):
                # This is a tag
                if content.name not in ['ul', 'ol']:
                    # Not a nested list, so include its text
                    text_parts.append(content.get_text())
            else:
                # This is direct text content
                text_parts.append(str(content))
        
        return ''.join(text_parts).strip()

    def extract_list_text(self, list_element) -> str:
        """Extract plain text from list elements without custom formatting
        
        DEPRECATED: This method is kept for backward compatibility.
        Use process_list_with_formatting() for proper Google Docs list formatting.
        """
        text_parts = []

        for li in list_element.find_all('li', recursive=False):
            # Get the text content of the list item and process math expressions
            li_text = li.get_text().strip()
            if li_text:
                # Process math expressions in the text
                processed_text, _ = self._process_math_in_text(li_text, [])
                text_parts.append(f"• {processed_text}")

            # Handle nested lists
            for nested_list in li.find_all(['ul', 'ol'], recursive=False):
                nested_text = self.extract_nested_list_text(nested_list, level=1)
                if nested_text:
                    text_parts.append(nested_text)

        return '\n'.join(text_parts)

    def extract_nested_list_text(self, list_element, level: int) -> str:
        """Extract text from nested lists with simple indentation
        
        DEPRECATED: This method is kept for backward compatibility.
        Use _process_list_at_level() for proper Google Docs list formatting.
        """
        text_parts = []
        indent = "  " * level  # Simple space indentation

        for li in list_element.find_all('li', recursive=False):
            li_text = li.get_text().strip()
            if li_text:
                # Process math expressions in nested list text
                processed_text, _ = self._process_math_in_text(li_text, [])
                text_parts.append(f"{indent}• {processed_text}")

            # Handle deeper nesting
            for nested_list in li.find_all(['ul', 'ol'], recursive=False):
                nested_text = self.extract_nested_list_text(nested_list, level + 1)
                if nested_text:
                    text_parts.append(nested_text)

        return '\n'.join(text_parts)

    def process_paragraph_with_formatting(self, element, start_index: int) -> Tuple[List[Dict], int]:
        """Process paragraph with proper bold and other formatting"""
        requests = []

        # Extract text content and formatting information
        text_parts = []
        formatting_info = []

        current_pos = 0
        for content in element.contents:
            if hasattr(content, 'name'):
                content_text = content.get_text()
                text_parts.append(content_text)

                # Track formatting for this text segment
                if content.name == 'strong' or content.name == 'b':
                    formatting_info.append({
                        'start': current_pos,
                        'end': current_pos + len(content_text),
                        'type': 'bold'
                    })
                elif content.name == 'em' or content.name == 'i':
                    formatting_info.append({
                        'start': current_pos,
                        'end': current_pos + len(content_text),
                        'type': 'italic'
                    })
                elif content.name == 'code':
                    formatting_info.append({
                        'start': current_pos,
                        'end': current_pos + len(content_text),
                        'type': 'code'
                    })

                current_pos += len(content_text)
            else:
                content_text = str(content)
                text_parts.append(content_text)
                current_pos += len(content_text)

        full_text = ''.join(text_parts)
        if not full_text.strip():
            return requests, 0

        # Process math expressions and update formatting info
        full_text, math_formatting = self._process_math_in_text(full_text, formatting_info)
        formatting_info.extend(math_formatting)
        
        # Process link placeholders and update formatting info
        full_text, link_formatting = self._process_links_in_text(full_text, formatting_info)
        formatting_info.extend(link_formatting)

        full_text_with_newline = full_text + '\n'
        text_length = grapheme_len(full_text_with_newline)

        # Insert the text
        requests.append({
            'insertText': {
                'location': {'index': start_index},
                'text': full_text_with_newline
            }
        })

        # Apply formatting
        for fmt in formatting_info:
            if fmt['type'] == 'bold':
                requests.append({
                    'updateTextStyle': {
                        'range': {
                            'startIndex': start_index + fmt['start'],
                            'endIndex': start_index + fmt['end']
                        },
                        'textStyle': {
                            'bold': True
                        },
                        'fields': 'bold'
                    }
                })
            elif fmt['type'] == 'italic':
                requests.append({
                    'updateTextStyle': {
                        'range': {
                            'startIndex': start_index + fmt['start'],
                            'endIndex': start_index + fmt['end']
                        },
                        'textStyle': {
                            'italic': True
                        },
                        'fields': 'italic'
                    }
                })
            elif fmt['type'] == 'code':
                requests.append({
                    'updateTextStyle': {
                        'range': {
                            'startIndex': start_index + fmt['start'],
                            'endIndex': start_index + fmt['end']
                        },
                        'textStyle': {
                            'weightedFontFamily': {
                                'fontFamily': 'Courier New'
                            },
                            'backgroundColor': {
                                'color': {
                                    'rgbColor': {
                                        'red': 0.0,
                                        'green': 0.0,
                                        'blue': 0.0
                                    }
                                }
                            },
                            'foregroundColor': {
                                'color': {
                                    'rgbColor': {
                                        'red': 1.0,
                                        'green': 1.0,
                                        'blue': 1.0
                                    }
                                }
                            }
                        },
                        'fields': 'weightedFontFamily,backgroundColor,foregroundColor'
                    }
                })
            elif fmt['type'] == 'math':
                requests.append({
                    'updateTextStyle': {
                        'range': {
                            'startIndex': start_index + fmt['start'],
                            'endIndex': start_index + fmt['end']
                        },
                        'textStyle': {
                            'italic': True,
                            'foregroundColor': {
                                'color': {
                                    'rgbColor': {
                                        'red': 0.0,
                                        'green': 0.4,
                                        'blue': 0.8
                                    }
                                }
                            }
                        },
                        'fields': 'italic,foregroundColor'
                    }
                })
            elif fmt['type'] == 'link':
                # Handle link formatting
                link_style = {
                    'foregroundColor': {
                        'color': {
                            'rgbColor': {
                                'red': 0.06,
                                'green': 0.46,
                                'blue': 0.86
                            }
                        }
                    },
                    'underline': True
                }
                
                # Add URL if it's an external link
                if 'url' in fmt:
                    link_style['link'] = {'url': fmt['url']}
                    fields = 'foregroundColor,underline,link'
                else:
                    fields = 'foregroundColor,underline'
                
                requests.append({
                    'updateTextStyle': {
                        'range': {
                            'startIndex': start_index + fmt['start'],
                            'endIndex': start_index + fmt['end']
                        },
                        'textStyle': link_style,
                        'fields': fields
                    }
                })

        return requests, text_length

    def process_paragraph_formatting(self, element) -> str:
        """Process paragraph with inline formatting"""
        text = ""
        for content in element.contents:
            if hasattr(content, 'name'):
                if content.name == 'strong' or content.name == 'b':
                    text += content.get_text()
                elif content.name == 'em' or content.name == 'i':
                    text += content.get_text()
                elif content.name == 'code':
                    text += content.get_text()
                elif content.name == 'a':
                    text += content.get_text()
                else:
                    text += content.get_text()
            else:
                text += str(content)
        return text















    def process_table(self, element, start_index: int) -> Tuple[List[Dict], int]:
        """Process table elements with proper Google Docs table support"""
        requests = []

        # Extract table data
        table_data = self.extract_table_data(element)

        if not table_data or len(table_data) == 0:
            # Empty table, just add a placeholder
            requests.append({
                'insertText': {
                    'location': {'index': start_index},
                    'text': '[Empty Table]\n'
                }
            })
            return requests, grapheme_len('[Empty Table]') + 1

        rows = len(table_data)
        cols = max(len(row) for row in table_data) if table_data else 1

        # For now, use simple text-based table as Google Docs table API is complex
        # and requires careful handling of table structure and cell references
        logger.debug(f"Processing table with {rows} rows and {cols} columns")

        # Use fallback text-based table formatting
        return self.create_simple_table_fallback(table_data, start_index)

    def extract_table_data(self, table_element) -> List[List[str]]:
        """Extract table data from HTML table element"""
        table_data = []

        # Find all rows
        rows = table_element.find_all('tr')

        for row in rows:
            row_data = []
            # Find all cells (th or td)
            cells = row.find_all(['th', 'td'])

            for cell in cells:
                # Get cell text and clean it up
                cell_text = cell.get_text().strip()
                # Handle line breaks within cells
                cell_text = cell_text.replace('\n', ' ').replace('\r', ' ')
                # Collapse multiple spaces
                cell_text = ' '.join(cell_text.split())
                row_data.append(cell_text)

            if row_data:  # Only add non-empty rows
                table_data.append(row_data)

        return table_data

    def create_simple_table_fallback(self, table_data: List[List[str]], start_index: int) -> Tuple[List[Dict], int]:
        """Create a simple text-based table as fallback when Google Docs table API fails"""
        requests = []
        table_text = ""

        if not table_data:
            return requests, 0

        # Calculate column widths for better formatting
        col_widths = []
        max_cols = max(len(row) for row in table_data) if table_data else 0

        for col_idx in range(max_cols):
            max_width = 0
            for row in table_data:
                if col_idx < len(row):
                    max_width = max(max_width, len(row[col_idx]))
            col_widths.append(min(max_width, 20))  # Cap at 20 characters

        # Create table header separator
        separator = "+" + "+".join(["-" * (width + 2) for width in col_widths]) + "+\n"

        # Format each row
        for row_idx, row_data in enumerate(table_data):
            # Add separator before first row and after header
            if row_idx == 0 or row_idx == 1:
                table_text += separator

            # Format row
            formatted_cells = []
            for col_idx in range(max_cols):
                cell_text = row_data[col_idx] if col_idx < len(row_data) else ""
                width = col_widths[col_idx]
                formatted_cells.append(f" {cell_text:<{width}} ")

            table_text += "|" + "|".join(formatted_cells) + "|\n"

        # Add final separator
        table_text += separator + "\n"

        requests.append({
            'insertText': {
                'location': {'index': start_index},
                'text': table_text
            }
        })

        return requests, grapheme_len(table_text)

    def process_obsidian_links(self, requests: List[Dict], markdown_content: str) -> List[Dict]:
        """Process Obsidian-specific syntax like [[links]] and external links"""
        # Links are now processed during paragraph formatting
        # This method is kept for compatibility but no longer needed
        return requests

    def process_images_in_markdown(self, markdown_content: str, note_path: Path) -> Tuple[str, List[Tuple[str, Path, str]]]:
        """Process markdown and return modified content with image placeholders"""
        image_positions = []
        # Clean whitespace-only lines first
        modified_content = clean_whitespace_lines(markdown_content)

        # Find embedded images and their positions
        embedded_image_pattern = r'!\[\[([^\]]+)\]\]'

        for match in re.finditer(embedded_image_pattern, markdown_content):
            image_name = match.group(1)
            image_path = self.file_manager.find_media_file(note_path.parent, image_name)

            if image_path and image_path.exists():
                # Create a unique placeholder that we can find later
                placeholder_id = f"IMAGE_PLACEHOLDER_{len(image_positions)}"
                image_positions.append((image_name, image_path, placeholder_id))

                logger.info(f"Found embedded image: {image_name}")

        # Replace all image syntax with placeholders
        for image_name, image_path, placeholder_id in image_positions:
            image_pattern = rf'!\[\[{re.escape(image_name)}\]\]'
            placeholder_text = f"\n[{placeholder_id}]\n"
            modified_content = re.sub(image_pattern, placeholder_text, modified_content)

        return modified_content, image_positions

    def preprocess_math_expressions(self, content: str) -> str:
        """
        Preprocess mathematical expressions in markdown content.
        Replaces LaTeX math expressions with placeholders for later formatting.

        Args:
            content: Markdown content with math expressions

        Returns:
            Content with math expressions replaced by placeholders
        """
        # Clear previous math expressions
        self.math_expressions = {}

        # Process block math first ($$...$$)
        block_math_pattern = r'\$\$([^$]+?)\$\$'
        content = self._replace_math_expressions(content, block_math_pattern, 'block')

        # Process inline math ($...$)
        inline_math_pattern = r'\$([^$\n]+?)\$'
        content = self._replace_math_expressions(content, inline_math_pattern, 'inline')

        return content

    def preprocess_links(self, content: str) -> Tuple[str, Dict]:
        """
        Preprocess links in markdown content and replace with placeholders.
        
        Args:
            content: Markdown content with links
            
        Returns:
            Tuple of (content with link placeholders, link_info dict)
        """
        # Clear previous link info
        self.link_info = {}
        
        # Process external links [text](url) first
        external_link_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
        content = self._replace_external_links(content, external_link_pattern)
        
        # Process internal Obsidian links [[link]] and [[link|alias]]
        internal_link_pattern = r'\[\[([^\]|]+)(\|([^\]]+))?\]\]'
        content = self._replace_internal_links(content, internal_link_pattern)
        
        return content, self.link_info

    def _replace_external_links(self, content: str, pattern: str) -> str:
        """Helper method to replace external links with placeholders"""
        def replace_match(match):
            link_text = match.group(1)
            url = match.group(2)
            placeholder_id = f"LINK_EXTERNAL_{len(self.link_info)}"
            
            self.link_info[placeholder_id] = {
                'text': link_text,
                'url': url,
                'type': 'external'
            }
            
            # Return just the link text as placeholder
            return f"[{placeholder_id}]{link_text}[/{placeholder_id}]"
        
        return re.sub(pattern, replace_match, content)

    def _replace_internal_links(self, content: str, pattern: str) -> str:
        """Helper method to replace internal links with placeholders"""
        def replace_match(match):
            link_target = match.group(1)
            alias = match.group(3) if match.group(3) else link_target
            placeholder_id = f"LINK_INTERNAL_{len(self.link_info)}"
            
            self.link_info[placeholder_id] = {
                'text': alias,
                'target': link_target,
                'type': 'internal'
            }
            
            # Return just the alias text as placeholder  
            return f"[{placeholder_id}]{alias}[/{placeholder_id}]"
        
        return re.sub(pattern, replace_match, content)

    def _replace_math_expressions(self, content: str, pattern: str, math_type: str) -> str:
        """Helper method to replace math expressions with placeholders"""
        def replace_match(match):
            math_content = match.group(1).strip()
            placeholder_id = f"MATH_{math_type.upper()}_{len(self.math_expressions)}"
            self.math_expressions[placeholder_id] = {
                'content': math_content,
                'type': math_type
            }
            return f"[{placeholder_id}]"

        return re.sub(pattern, replace_match, content)

    def _process_math_in_text(self, text: str, existing_formatting: List[Dict]) -> Tuple[str, List[Dict]]:
        """
        Process math placeholders in text and return updated text with math formatting info.

        Args:
            text: Text containing math placeholders
            existing_formatting: Existing formatting information

        Returns:
            Tuple of (updated_text, math_formatting_info)
        """
        math_formatting = []
        updated_text = text
        offset = 0  # Track position changes due to replacements

        # Find all math placeholders
        placeholder_pattern = r'\[MATH_(BLOCK|INLINE)_(\d+)\]'

        for match in re.finditer(placeholder_pattern, text):
            placeholder_full = match.group(0)  # [MATH_BLOCK_0]
            placeholder_key = placeholder_full[1:-1]  # MATH_BLOCK_0 (remove brackets)
            math_type = match.group(1).lower()

            if placeholder_key in self.math_expressions:
                math_info = self.math_expressions[placeholder_key]
                math_content = math_info['content']

                # Format math content for display
                if math_type == 'block':
                    # Block math: add some spacing and formatting
                    formatted_math = f"\n[MATH] {math_content}\n"
                else:
                    # Inline math: just add prefix
                    formatted_math = f"[MATH: {math_content}]"

                # Calculate positions
                start_pos = match.start() + offset
                end_pos = start_pos + len(formatted_math)

                # Add math formatting info
                math_formatting.append({
                    'start': start_pos,
                    'end': end_pos,
                    'type': 'math'
                })

                # Replace placeholder with formatted math
                updated_text = updated_text[:start_pos] + formatted_math + updated_text[match.end() + offset:]
                offset += len(formatted_math) - len(placeholder_full)

        return updated_text, math_formatting

    def _process_links_in_text(self, text: str, existing_formatting: List[Dict]) -> Tuple[str, List[Dict]]:
        """
        Process link placeholders in text and return updated text with link formatting info.

        Args:
            text: Text containing link placeholders
            existing_formatting: Existing formatting information

        Returns:
            Tuple of (updated_text, link_formatting_info)
        """
        link_formatting = []
        updated_text = text
        offset = 0  # Track position changes due to replacements

        # Find all link placeholders [LINK_TYPE_X]text[/LINK_TYPE_X]
        placeholder_pattern = r'\[(LINK_(?:EXTERNAL|INTERNAL)_\d+)\]([^\[]*)\[/\1\]'

        for match in re.finditer(placeholder_pattern, text):
            placeholder_key = match.group(1)  # LINK_EXTERNAL_0 or LINK_INTERNAL_0
            link_text = match.group(2)  # The actual text to display
            
            if placeholder_key in self.link_info:
                link_info = self.link_info[placeholder_key]
                
                # Calculate positions in the updated text
                start_pos = match.start() + offset
                end_pos = start_pos + len(link_text)
                
                # Create link formatting info
                link_format = {
                    'start': start_pos,
                    'end': end_pos,
                    'type': 'link'
                }
                
                # Add URL for external links
                if link_info['type'] == 'external':
                    link_format['url'] = link_info['url']
                    logger.info(f"Processing external link: {link_text} -> {link_info['url']}")
                else:
                    logger.info(f"Processing internal link: {link_text} -> {link_info['target']}")
                
                link_formatting.append(link_format)
                
                # Replace the placeholder with just the link text
                full_placeholder = match.group(0)
                updated_text = updated_text[:start_pos] + link_text + updated_text[match.end() + offset:]
                offset += len(link_text) - len(full_placeholder)

        return updated_text, link_formatting
