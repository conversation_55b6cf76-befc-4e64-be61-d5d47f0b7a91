"""
Utility functions for Obsidian to Google Sync
"""

import grapheme
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('google_sync.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def grapheme_len(text: str) -> int:
    """
    Calculate the length of text in grapheme clusters (user-perceived characters)
    instead of Unicode code points. This is essential for Google Docs API
    which expects indices based on grapheme clusters.

    Args:
        text: The text to measure

    Returns:
        Number of grapheme clusters in the text
    """
    return grapheme.length(text)


def clean_whitespace_lines(content: str) -> str:
    """
    Clean up lines that contain only whitespace (spaces, tabs) and convert them to empty lines.
    This ensures consistent formatting in the final Google Doc.

    Args:
        content: The markdown content to clean

    Returns:
        Cleaned content with whitespace-only lines converted to empty lines
    """
    lines = content.split('\n')
    cleaned_lines = []

    for line in lines:
        # If line contains only whitespace (spaces, tabs, etc.), make it empty
        if line.strip() == '':
            cleaned_lines.append('')
        else:
            cleaned_lines.append(line)

    return '\n'.join(cleaned_lines)
