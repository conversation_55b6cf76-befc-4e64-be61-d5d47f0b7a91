"""
Google Authentication Manager
Handles OAuth2 authentication for Google APIs
"""

import os
from typing import Optional
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from .utils import logger


class GoogleAuthManager:
    """Manages Google API authentication"""
    
    def __init__(self, credentials_path: str):
        """
        Initialize the authentication manager
        
        Args:
            credentials_path: Path to Google OAuth2 credentials JSON file
        """
        self.credentials_path = credentials_path
        self.creds = None
        
        # Google API scopes
        self.scopes = [
            'https://www.googleapis.com/auth/drive',
            'https://www.googleapis.com/auth/documents'
        ]
    
    def authenticate(self) -> bool:
        """Authenticate with Google APIs using OAuth2"""
        try:
            token_path = 'google_token.json'
            
            # Load existing token if available
            if os.path.exists(token_path):
                self.creds = Credentials.from_authorized_user_file(token_path, self.scopes)
            
            # If there are no (valid) credentials available, let the user log in
            if not self.creds or not self.creds.valid:
                if self.creds and self.creds.expired and self.creds.refresh_token:
                    self.creds.refresh(Request())
                else:
                    if not os.path.exists(self.credentials_path):
                        logger.error(f"Credentials file not found: {self.credentials_path}")
                        logger.error("Please download credentials.json from Google Cloud Console")
                        return False
                        
                    flow = InstalledAppFlow.from_client_secrets_file(
                        self.credentials_path, self.scopes)
                    self.creds = flow.run_local_server(port=0)
                
                # Save the credentials for the next run
                with open(token_path, 'w') as token:
                    token.write(self.creds.to_json())
            
            logger.info("Google authentication successful")
            return True
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return False
    
    def get_drive_service(self):
        """Get Google Drive service object"""
        if not self.creds:
            raise ValueError("Not authenticated. Call authenticate() first.")
        return build('drive', 'v3', credentials=self.creds)
    
    def get_docs_service(self):
        """Get Google Docs service object"""
        if not self.creds:
            raise ValueError("Not authenticated. Call authenticate() first.")
        return build('docs', 'v1', credentials=self.creds)
