"""
Main Sync Class
Coordinates the synchronization between Obsidian vault and Google Drive/Docs
"""

import os
import json
from pathlib import Path
from typing import Optional, List, Tuple
from datetime import datetime
from .auth import GoogleAuthManager
from .drive_manager import GoogleDriveManager
from .docs_manager import GoogleDocsManager
from .file_manager import FileManager
from .markdown_processor import MarkdownProcessor
from .error_handler import <PERSON>rror<PERSON><PERSON><PERSON>, RetryConfig
from .link_manager import LinkManager
from .config_manager import ConfigManager
from .sync_state import SyncState, SyncStatus
from .utils import logger, grapheme_len


class ObsidianToGoogleSync:
    """Main synchronization class with enhanced error handling and state management"""

    def __init__(self, obsidian_path: str, credentials_path: str, config_file: str = None):
        """
        Initialize the Google sync tool

        Args:
            obsidian_path: Path to Obsidian vault
            credentials_path: Path to Google OAuth2 credentials JSON file
            config_file: Optional path to configuration file
        """
        self.obsidian_path = Path(obsidian_path)
        self.credentials_path = credentials_path

        # Initialize configuration and state management
        self.config_manager = ConfigManager(config_file or "sync_config.json")
        self.sync_state = SyncState()

        # Initialize error handler with retry configuration
        retry_config = RetryConfig(
            max_attempts=self.config_manager.get_retry_config().max_attempts,
            base_delay=self.config_manager.get_retry_config().base_delay,
            max_delay=self.config_manager.get_retry_config().max_delay
        )
        self.error_handler = ErrorHandler(retry_config)

        # Initialize managers
        self.auth_manager = GoogleAuthManager(credentials_path)
        self.file_manager = FileManager(obsidian_path)
        self.markdown_processor = MarkdownProcessor(self.file_manager)

        # These will be initialized after authentication
        self.drive_manager = None
        self.docs_manager = None
        self.link_manager = None

        # Folder mapping: obsidian_path -> google_drive_folder_id
        self.folder_mapping = {}

        # Track upload failures (legacy support)
        self.failed_uploads = []
    
    def authenticate(self) -> bool:
        """Authenticate with Google APIs with error handling"""
        def auth_operation():
            return self.auth_manager.authenticate()

        try:
            success = self.error_handler.execute_with_retry(
                auth_operation,
                "google_authentication",
                {"credentials_path": self.credentials_path}
            )

            if success:
                # Initialize service managers
                drive_service = self.auth_manager.get_drive_service()
                docs_service = self.auth_manager.get_docs_service()

                self.drive_manager = GoogleDriveManager(drive_service)
                self.docs_manager = GoogleDocsManager(docs_service, drive_service)

                # Initialize link manager
                self.link_manager = LinkManager(
                    self.file_manager,
                    self.docs_manager,
                    self.drive_manager
                )

                logger.info("Authentication successful")
                return True
            else:
                logger.error("Authentication failed")
                return False

        except Exception as e:
            logger.error(f"Authentication failed with error: {e}")
            return False
    
    def setup_folder_structure(self, root_folder_name: str = "Obsidian Sync") -> Optional[str]:
        """Setup the main folder structure in Google Drive"""
        try:
            # Create or find the main sync folder
            main_folder_id = self.drive_manager.find_or_create_folder(root_folder_name)
            
            if not main_folder_id:
                logger.error(f"Failed to create main folder: {root_folder_name}")
                return None
            
            logger.info(f"Main sync folder ready: {root_folder_name} (ID: {main_folder_id})")
            return main_folder_id
            
        except Exception as e:
            logger.error(f"Error setting up folder structure: {e}")
            return None
    
    def sync_notes(self, root_folder_name: str = "Obsidian Sync") -> bool:
        """Main sync function with enhanced state management and error handling"""
        try:
            logger.info("Starting Obsidian to Google sync...")

            # Authenticate first
            if not self.authenticate():
                logger.error("Authentication failed")
                return False

            # Setup folder structure
            main_folder_id = self.setup_folder_structure(root_folder_name)
            if not main_folder_id:
                return False

            # Get all notes from Obsidian vault
            notes = self.file_manager.get_obsidian_notes()
            if not notes:
                logger.warning("No notes found to sync")
                return True

            # Start sync session with state tracking
            config_snapshot = {
                'root_folder': root_folder_name,
                'total_notes': len(notes),
                'config': self.config_manager.config.__dict__
            }
            session_id = self.sync_state.start_session(len(notes), config_snapshot)

            # Initialize all notes as pending
            for note_path in notes:
                self.sync_state.mark_item_status(str(note_path), SyncStatus.PENDING)

            # Organize notes by folder
            organized_notes = self.file_manager.organize_notes_by_folder(notes)

            # Process each folder
            for folder_name, folder_notes in organized_notes.items():
                logger.info(f"Processing folder: {folder_name}")

                # Create folder in Google Drive if it doesn't exist
                if folder_name == "Root":
                    folder_id = main_folder_id
                else:
                    folder_id = self._create_folder_with_retry(folder_name, main_folder_id)

                if not folder_id:
                    logger.error(f"Failed to create folder: {folder_name}")
                    # Mark all notes in this folder as failed
                    for note_path in folder_notes:
                        self.sync_state.mark_failed(
                            str(note_path),
                            f"Failed to create folder: {folder_name}",
                            "create_folder"
                        )
                    continue

                self.folder_mapping[folder_name] = folder_id

                # Sync notes in this folder
                for note_path in folder_notes:
                    self.sync_state.update_progress(str(note_path))

                    try:
                        logger.info(f"Syncing note: {note_path.name}")
                        success = self._sync_single_note_with_retry(note_path, folder_id)

                        if success:
                            self.sync_state.mark_completed(str(note_path))
                            # Update link registry
                            if self.link_manager:
                                # This will be set when doc is created
                                pass
                        else:
                            self.sync_state.mark_failed(
                                str(note_path),
                                "Sync operation failed",
                                "sync_note"
                            )

                    except Exception as e:
                        logger.error(f"Error syncing note {note_path.name}: {e}")
                        self.sync_state.mark_failed(str(note_path), str(e), "sync_note")
                        continue

            # End sync session
            self.sync_state.end_session(SyncStatus.COMPLETED)

            # Print summary
            progress_summary = self.sync_state.get_progress_summary()
            logger.info(f"Sync completed: {progress_summary['completed']}/{progress_summary['total_items']} notes processed successfully")

            if progress_summary['failed'] > 0:
                logger.warning(f"{progress_summary['failed']} files failed to process. Check sync state for details.")

            # Save legacy failed uploads for compatibility
            self._update_legacy_failed_uploads()

            return True

        except Exception as e:
            logger.error(f"Sync failed: {e}")
            if self.sync_state.current_session:
                self.sync_state.end_session(SyncStatus.FAILED)
            return False
    
    def _create_folder_with_retry(self, folder_name: str, parent_id: str) -> Optional[str]:
        """Create folder with retry logic"""
        def create_operation():
            return self.drive_manager.find_or_create_folder(folder_name, parent_id)

        try:
            return self.error_handler.execute_with_retry(
                create_operation,
                "create_folder",
                {"folder_name": folder_name, "parent_id": parent_id}
            )
        except Exception as e:
            logger.error(f"Failed to create folder {folder_name}: {e}")
            return None

    def _sync_single_note_with_retry(self, note_path: Path, folder_id: str) -> bool:
        """Sync single note with retry logic"""
        def sync_operation():
            return self.sync_single_note(note_path, folder_id)

        try:
            return self.error_handler.execute_with_retry(
                sync_operation,
                "sync_note",
                {"note_path": str(note_path), "folder_id": folder_id}
            )
        except Exception as e:
            logger.error(f"Failed to sync note {note_path.name}: {e}")
            return False

    def _update_legacy_failed_uploads(self):
        """Update legacy failed uploads list for compatibility"""
        self.failed_uploads = []
        for failed_item in self.sync_state.get_failed_items():
            self.failed_uploads.append({
                'file_path': failed_item.file_path,
                'action': failed_item.action,
                'error': failed_item.error,
                'timestamp': failed_item.timestamp
            })

    def sync_single_note(self, note_path: Path, folder_id: str) -> bool:
        """Sync a single note to Google Docs with enhanced link and formatting support"""
        try:
            # Read note content
            with open(note_path, 'r', encoding='utf-8') as f:
                markdown_content = f.read()

            if not markdown_content.strip():
                logger.warning(f"Note {note_path.name} is empty, skipping")
                return True

            # Validate and process internal links
            if self.link_manager and self.config_manager.get_link_config().validate_internal_links:
                link_validations = self.link_manager.validate_internal_links(
                    markdown_content, note_path.stem
                )

                # Create missing target documents if enabled
                if self.config_manager.get_link_config().create_missing_targets:
                    missing_links = [lv for lv in link_validations if not lv.exists]
                    if missing_links:
                        logger.info(f"Creating {len(missing_links)} missing target documents")
                        created_docs = self.link_manager.create_missing_target_docs(
                            missing_links, folder_id
                        )
                        logger.info(f"Created {len(created_docs)} missing target documents")

            # Process images in markdown
            modified_content, image_positions = self.markdown_processor.process_images_in_markdown(
                markdown_content, note_path
            )

            # Upload images to Google Drive first
            image_positions_with_urls = []
            for image_name, image_path, placeholder_id in image_positions:
                result = self.drive_manager.upload_media_file(image_path, folder_id)
                if result:
                    file_id, public_url = result
                    image_positions_with_urls.append((image_name, image_path, public_url, placeholder_id))
                    logger.info(f"Uploaded image: {image_name}")
                else:
                    logger.warning(f"Failed to upload image: {image_name}")

            # Convert markdown to Google Docs requests
            content_requests = self.markdown_processor.convert_markdown_to_docs_requests(
                modified_content, note_path
            )

            # Create or update Google Doc
            doc_title = note_path.stem  # Filename without extension
            doc_id = self.docs_manager.create_google_doc(doc_title, content_requests, folder_id)

            if doc_id:
                # Update link registry with successful doc creation
                if self.link_manager:
                    self.link_manager.update_link_registry(note_path, doc_id)

                # Insert images into the document
                if image_positions_with_urls:
                    self.insert_embedded_images(doc_id, image_positions_with_urls)

                logger.info(f"Successfully synced note: {note_path.name}")
                return True
            else:
                logger.error(f"Failed to create/update document for note: {note_path.name}")
                return False

        except Exception as e:
            logger.error(f"Error syncing single note {note_path.name}: {e}")
            return False
    
    def insert_embedded_images(self, doc_id: str, image_positions: List[Tuple[str, Path, str, str]]):
        """Insert embedded images into the Google Doc at placeholder positions"""
        try:
            for image_name, image_path, image_url, placeholder_id in image_positions:
                # Find and replace the placeholder with the actual image
                success = self.docs_manager.replace_placeholder_with_image(
                    doc_id, f"[{placeholder_id}]", image_url, image_name
                )

                if success:
                    logger.info(f"Replaced placeholder {placeholder_id} with image {image_name}")
                else:
                    logger.warning(f"Failed to replace placeholder {placeholder_id} with image {image_name}")

        except Exception as e:
            logger.error(f"Error inserting embedded images: {e}")
    
    def save_failed_uploads(self):
        """Save failed uploads to JSON file for retry"""
        if self.failed_uploads:
            failed_file = "failed_uploads.json"
            
            # Combine with existing failed uploads if file exists
            existing_failures = []
            if os.path.exists(failed_file):
                try:
                    with open(failed_file, 'r', encoding='utf-8') as f:
                        existing_failures = json.load(f)
                except Exception as e:
                    logger.warning(f"Could not read existing failed uploads: {e}")
            
            # Add timestamp to new failures
            timestamp = datetime.now().isoformat()
            for failure in self.failed_uploads:
                failure['timestamp'] = timestamp
            
            # Combine and save
            all_failures = existing_failures + self.failed_uploads
            
            with open(failed_file, 'w', encoding='utf-8') as f:
                json.dump(all_failures, f, indent=2)
            
            logger.info(f"Saved {len(self.failed_uploads)} failed uploads to {failed_file}")
    
    def retry_failed_uploads(self) -> bool:
        """Retry previously failed uploads"""
        try:
            failed_file = "failed_uploads.json"
            
            if not os.path.exists(failed_file):
                logger.info("No failed uploads file found")
                return True
            
            # Load failed uploads
            with open(failed_file, 'r', encoding='utf-8') as f:
                failed_uploads = json.load(f)
            
            if not failed_uploads:
                logger.info("No failed uploads to retry")
                return True
            
            logger.info(f"Retrying {len(failed_uploads)} failed uploads...")
            
            # Clear current failed list
            self.failed_uploads = []
            
            # Retry each failed upload
            retry_success = 0
            for failure in failed_uploads:
                file_path = failure['file_path']
                action = failure['action']
                
                logger.info(f"Retrying {action}: {file_path}")
                
                # This would need specific retry logic based on action type
                # For now, just log the attempt
                logger.info(f"  Retry logic for {action} not yet implemented")
            
            logger.info(f"Retry completed: {retry_success}/{len(failed_uploads)} successful")
            
            # Remove the failed file if all retries succeeded
            if not self.failed_uploads:
                os.remove(failed_file)
                logger.info(f"Removed {failed_file} - all retries successful")
            
            return True
            
        except Exception as e:
            logger.error(f"Error retrying failed uploads: {e}")
            return False
    
    def create_config_file(self, config_path: str = "google_sync_config.json"):
        """Create a configuration file template"""
        config = {
            "obsidian_path": "./obsidian",
            "credentials_path": "credentials.json",
            "root_folder_name": "Obsidian Sync",
            "excluded_folders": [".obsidian", ".trash"],
            "excluded_files": ["*.tmp", "*.bak"]
        }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"Configuration file created: {config_path}")
        print(f"Please edit {config_path} and add your Google credentials.json file")
