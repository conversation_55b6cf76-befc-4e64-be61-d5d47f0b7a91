"""
Main Sync Class
Coordinates the synchronization between Obsidian vault and Google Drive/Docs
"""

import os
import json
from pathlib import Path
from typing import Optional, List, Tuple
from datetime import datetime
from .auth import GoogleAuthManager
from .drive_manager import GoogleDriveManager
from .docs_manager import GoogleDocsManager
from .file_manager import FileManager
from .markdown_processor import MarkdownProcessor
from .utils import logger, grapheme_len


class ObsidianToGoogleSync:
    """Main synchronization class"""
    
    def __init__(self, obsidian_path: str, credentials_path: str):
        """
        Initialize the Google sync tool
        
        Args:
            obsidian_path: Path to Obsidian vault
            credentials_path: Path to Google OAuth2 credentials JSON file
        """
        self.obsidian_path = Path(obsidian_path)
        self.credentials_path = credentials_path
        
        # Initialize managers
        self.auth_manager = GoogleAuthManager(credentials_path)
        self.file_manager = FileManager(obsidian_path)
        self.markdown_processor = MarkdownProcessor(self.file_manager)
        
        # These will be initialized after authentication
        self.drive_manager = None
        self.docs_manager = None
        
        # Folder mapping: obsidian_path -> google_drive_folder_id
        self.folder_mapping = {}
        
        # Track upload failures
        self.failed_uploads = []
    
    def authenticate(self) -> bool:
        """Authenticate with Google APIs using OAuth2"""
        if self.auth_manager.authenticate():
            # Initialize service managers
            drive_service = self.auth_manager.get_drive_service()
            docs_service = self.auth_manager.get_docs_service()
            
            self.drive_manager = GoogleDriveManager(drive_service)
            self.docs_manager = GoogleDocsManager(docs_service, drive_service)
            
            return True
        return False
    
    def setup_folder_structure(self, root_folder_name: str = "Obsidian Sync") -> Optional[str]:
        """Setup the main folder structure in Google Drive"""
        try:
            # Create or find the main sync folder
            main_folder_id = self.drive_manager.find_or_create_folder(root_folder_name)
            
            if not main_folder_id:
                logger.error(f"Failed to create main folder: {root_folder_name}")
                return None
            
            logger.info(f"Main sync folder ready: {root_folder_name} (ID: {main_folder_id})")
            return main_folder_id
            
        except Exception as e:
            logger.error(f"Error setting up folder structure: {e}")
            return None
    
    def sync_notes(self, root_folder_name: str = "Obsidian Sync") -> bool:
        """Main sync function"""
        try:
            logger.info("Starting Obsidian to Google sync...")
            
            # Authenticate first
            if not self.authenticate():
                logger.error("Authentication failed")
                return False
            
            # Setup folder structure
            main_folder_id = self.setup_folder_structure(root_folder_name)
            if not main_folder_id:
                return False
            
            # Get all notes from Obsidian vault
            notes = self.file_manager.get_obsidian_notes()
            if not notes:
                logger.warning("No notes found to sync")
                return True
            
            # Organize notes by folder
            organized_notes = self.file_manager.organize_notes_by_folder(notes)
            
            # Process each folder
            for folder_name, folder_notes in organized_notes.items():
                logger.info(f"Processing folder: {folder_name}")
                
                # Create folder in Google Drive if it doesn't exist
                if folder_name == "Root":
                    folder_id = main_folder_id
                else:
                    folder_id = self.drive_manager.find_or_create_folder(folder_name, main_folder_id)
                
                if not folder_id:
                    logger.error(f"Failed to create folder: {folder_name}")
                    continue
                
                self.folder_mapping[folder_name] = folder_id
                
                # Sync notes in this folder
                for note_path in folder_notes:
                    try:
                        logger.info(f"Syncing note: {note_path.name}")
                        self.sync_single_note(note_path, folder_id)
                    except Exception as e:
                        logger.error(f"Error syncing note {note_path.name}: {e}")
                        self.failed_uploads.append({
                            'file_path': str(note_path),
                            'action': 'sync_note',
                            'error': str(e)
                        })
                        continue
            
            # Save failed uploads for retry
            self.save_failed_uploads()
            
            # Print summary
            total_notes = len(notes)
            failed_count = len(self.failed_uploads)
            success_count = total_notes - failed_count
            
            logger.info(f"Sync completed: {success_count}/{total_notes} notes processed successfully")
            
            if failed_count > 0:
                logger.warning(f"{failed_count} files failed to process. Check failed_uploads.json for details.")
            
            return True
            
        except Exception as e:
            logger.error(f"Sync failed: {e}")
            return False
    
    def sync_single_note(self, note_path: Path, folder_id: str) -> bool:
        """Sync a single note to Google Docs"""
        try:
            # Read note content
            with open(note_path, 'r', encoding='utf-8') as f:
                markdown_content = f.read()
            
            if not markdown_content.strip():
                logger.warning(f"Note {note_path.name} is empty, skipping")
                return True
            
            # Process images in markdown
            modified_content, image_positions = self.markdown_processor.process_images_in_markdown(
                markdown_content, note_path
            )
            
            # Upload images to Google Drive first
            image_positions_with_urls = []
            for image_name, image_path, placeholder_id in image_positions:
                result = self.drive_manager.upload_media_file(image_path, folder_id)
                if result:
                    file_id, public_url = result
                    image_positions_with_urls.append((image_name, image_path, public_url, placeholder_id))
                    logger.info(f"Uploaded image: {image_name}")
                else:
                    logger.warning(f"Failed to upload image: {image_name}")
            
            # Convert markdown to Google Docs requests
            content_requests = self.markdown_processor.convert_markdown_to_docs_requests(
                modified_content, note_path
            )
            
            # Create or update Google Doc
            doc_title = note_path.stem  # Filename without extension
            doc_id = self.docs_manager.create_google_doc(doc_title, content_requests, folder_id)
            
            if doc_id:
                # Insert images into the document
                if image_positions_with_urls:
                    self.insert_embedded_images(doc_id, image_positions_with_urls)
                
                logger.info(f"Successfully synced note: {note_path.name}")
                return True
            else:
                logger.error(f"Failed to create/update document for note: {note_path.name}")
                return False
                
        except Exception as e:
            logger.error(f"Error syncing single note {note_path.name}: {e}")
            return False
    
    def insert_embedded_images(self, doc_id: str, image_positions: List[Tuple[str, Path, str, str]]):
        """Insert embedded images into the Google Doc at placeholder positions"""
        try:
            for image_name, image_path, image_url, placeholder_id in image_positions:
                # Find and replace the placeholder with the actual image
                success = self.docs_manager.replace_placeholder_with_image(
                    doc_id, f"[{placeholder_id}]", image_url, image_name
                )

                if success:
                    logger.info(f"Replaced placeholder {placeholder_id} with image {image_name}")
                else:
                    logger.warning(f"Failed to replace placeholder {placeholder_id} with image {image_name}")

        except Exception as e:
            logger.error(f"Error inserting embedded images: {e}")
    
    def save_failed_uploads(self):
        """Save failed uploads to JSON file for retry"""
        if self.failed_uploads:
            failed_file = "failed_uploads.json"
            
            # Combine with existing failed uploads if file exists
            existing_failures = []
            if os.path.exists(failed_file):
                try:
                    with open(failed_file, 'r', encoding='utf-8') as f:
                        existing_failures = json.load(f)
                except Exception as e:
                    logger.warning(f"Could not read existing failed uploads: {e}")
            
            # Add timestamp to new failures
            timestamp = datetime.now().isoformat()
            for failure in self.failed_uploads:
                failure['timestamp'] = timestamp
            
            # Combine and save
            all_failures = existing_failures + self.failed_uploads
            
            with open(failed_file, 'w', encoding='utf-8') as f:
                json.dump(all_failures, f, indent=2)
            
            logger.info(f"Saved {len(self.failed_uploads)} failed uploads to {failed_file}")
    
    def retry_failed_uploads(self) -> bool:
        """Retry previously failed uploads"""
        try:
            failed_file = "failed_uploads.json"
            
            if not os.path.exists(failed_file):
                logger.info("No failed uploads file found")
                return True
            
            # Load failed uploads
            with open(failed_file, 'r', encoding='utf-8') as f:
                failed_uploads = json.load(f)
            
            if not failed_uploads:
                logger.info("No failed uploads to retry")
                return True
            
            logger.info(f"Retrying {len(failed_uploads)} failed uploads...")
            
            # Clear current failed list
            self.failed_uploads = []
            
            # Retry each failed upload
            retry_success = 0
            for failure in failed_uploads:
                file_path = failure['file_path']
                action = failure['action']
                
                logger.info(f"Retrying {action}: {file_path}")
                
                # This would need specific retry logic based on action type
                # For now, just log the attempt
                logger.info(f"  Retry logic for {action} not yet implemented")
            
            logger.info(f"Retry completed: {retry_success}/{len(failed_uploads)} successful")
            
            # Remove the failed file if all retries succeeded
            if not self.failed_uploads:
                os.remove(failed_file)
                logger.info(f"Removed {failed_file} - all retries successful")
            
            return True
            
        except Exception as e:
            logger.error(f"Error retrying failed uploads: {e}")
            return False
    
    def create_config_file(self, config_path: str = "google_sync_config.json"):
        """Create a configuration file template"""
        config = {
            "obsidian_path": "./obsidian",
            "credentials_path": "credentials.json",
            "root_folder_name": "Obsidian Sync",
            "excluded_folders": [".obsidian", ".trash"],
            "excluded_files": ["*.tmp", "*.bak"]
        }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"Configuration file created: {config_path}")
        print(f"Please edit {config_path} and add your Google credentials.json file")
