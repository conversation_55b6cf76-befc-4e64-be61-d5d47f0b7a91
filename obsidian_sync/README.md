# Obsidian Sync Module

Module này đã được tách từ file `obsidian_to_google_sync.py` thành các file con để có cấu trúc dự án tốt hơn và dễ bảo trì hơn.

## Cấu trúc <PERSON>dule

```
obsidian_sync/
├── __init__.py           # Package initialization và exports
├── auth.py              # Google authentication management
├── drive_manager.py     # Google Drive operations (folders, files)
├── docs_manager.py      # Google Docs operations (documents, formatting)
├── markdown_processor.py # Markdown to Google Docs conversion
├── file_manager.py      # Obsidian vault file operations
├── sync.py             # Main synchronization coordinator
└── utils.py            # Utility functions (logging, text processing)
```

## Mô tả các Module

### `auth.py` - GoogleAuthManager

- Xử lý OAuth2 authentication với Google APIs
- Quản lý tokens và credentials
- Cung cấp authenticated service objects

### `drive_manager.py` - GoogleDriveManager

- Tạo và quản lý folders trong Google Drive
- Upload và update media files
- <PERSON><PERSON><PERSON> kiếm files và folders existing

### `docs_manager.py` - GoogleDocsManager

- Tạo và update Google Docs
- Apply formatting và content requests
- Xử lý document operations
- Fallback methods cho các lỗi phức tạp

### `markdown_processor.py` - MarkdownProcessor

- Convert Obsidian markdown sang Google Docs API requests
- Xử lý tables, lists, headers, code blocks
- Process images và media references
- Handle Obsidian-specific syntax

### `file_manager.py` - FileManager

- Đọc và organize Obsidian vault files
- Filter files theo allowed.list
- Tìm media files trong attachment folders
- Organize notes theo folder structure

### `sync.py` - ObsidianToGoogleSync

- Main coordinator class
- Orchestrate toàn bộ sync process
- Handle errors và failed uploads
- Configuration management

### `utils.py`

- Logging configuration
- Text processing utilities (grapheme_len, clean_whitespace_lines)
- Shared utility functions

## Cách sử dụng

### Từ main.py (như trước):

```bash
python main.py --obsidian-path ./obsidian --credentials credentials.json
```

### Import module trong code khác:

```python
from obsidian_sync import ObsidianToGoogleSync

# Initialize
sync = ObsidianToGoogleSync("./obsidian", "credentials.json")

# Run sync
success = sync.sync_notes("My Obsidian Sync")
```

## Lợi ích của việc tách module

1. **Separation of Concerns**: Mỗi file có trách nhiệm rõ ràng
2. **Maintainability**: Dễ maintain và debug từng phần
3. **Testability**: Có thể test từng component riêng biệt
4. **Reusability**: Có thể reuse từng manager cho các dự án khác
5. **Readability**: Code dễ đọc và hiểu hơn
6. **Scalability**: Dễ mở rộng thêm features mới

## Migration từ file cũ

File `obsidian_to_google_sync.py` cũ vẫn có thể hoạt động, nhưng khuyến khích sử dụng module mới:

1. Import từ `obsidian_sync` thay vì import trực tiếp
2. Sử dụng `main.py` thay vì run file cũ trực tiếp
3. Code structure mới giúp debug và maintain dễ hơn
