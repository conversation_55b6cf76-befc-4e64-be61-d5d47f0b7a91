"""
Sync State Manager
Tracks sync progress and enables resumable operations
"""

import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from .utils import logger


class SyncStatus(Enum):
    """Status of sync operations"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class FailedItem:
    """Represents a failed sync item"""
    file_path: str
    action: str
    error: str
    timestamp: str
    retry_count: int = 0
    last_retry: Optional[str] = None


@dataclass
class SyncProgress:
    """Tracks progress of sync operation"""
    total_items: int
    completed_items: int
    failed_items: int
    skipped_items: int
    current_item: Optional[str] = None
    start_time: Optional[str] = None
    last_update: Optional[str] = None
    
    @property
    def completion_percentage(self) -> float:
        """Calculate completion percentage"""
        if self.total_items == 0:
            return 100.0
        return (self.completed_items + self.failed_items + self.skipped_items) / self.total_items * 100


@dataclass
class SyncSession:
    """Represents a sync session"""
    session_id: str
    start_time: str
    end_time: Optional[str] = None
    status: str = SyncStatus.IN_PROGRESS.value
    progress: Optional[SyncProgress] = None
    config_snapshot: Optional[Dict] = None


class SyncState:
    """Manages sync state and progress tracking"""
    
    def __init__(self, state_file: str = "sync_state.json"):
        """
        Initialize sync state manager
        
        Args:
            state_file: Path to state file
        """
        self.state_file = Path(state_file)
        self.current_session: Optional[SyncSession] = None
        self.item_status: Dict[str, SyncStatus] = {}
        self.failed_items: List[FailedItem] = []
        self.progress: Optional[SyncProgress] = None
        self.load_state()
    
    def load_state(self):
        """Load state from file"""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Load current session
                if 'current_session' in data and data['current_session']:
                    session_data = data['current_session']
                    progress_data = session_data.get('progress')
                    
                    self.current_session = SyncSession(
                        session_id=session_data['session_id'],
                        start_time=session_data['start_time'],
                        end_time=session_data.get('end_time'),
                        status=session_data.get('status', SyncStatus.IN_PROGRESS.value),
                        progress=SyncProgress(**progress_data) if progress_data else None,
                        config_snapshot=session_data.get('config_snapshot')
                    )
                    
                    if self.current_session.progress:
                        self.progress = self.current_session.progress
                
                # Load item status
                if 'item_status' in data:
                    self.item_status = {
                        path: SyncStatus(status) 
                        for path, status in data['item_status'].items()
                    }
                
                # Load failed items
                if 'failed_items' in data:
                    self.failed_items = [
                        FailedItem(**item_data) 
                        for item_data in data['failed_items']
                    ]
                
                logger.info(f"Loaded sync state from {self.state_file}")
            else:
                logger.info("No existing sync state found")
                
        except Exception as e:
            logger.error(f"Error loading sync state: {e}")
            self._reset_state()
    
    def save_state(self):
        """Save current state to file"""
        try:
            data = {
                'current_session': asdict(self.current_session) if self.current_session else None,
                'item_status': {
                    path: status.value 
                    for path, status in self.item_status.items()
                },
                'failed_items': [asdict(item) for item in self.failed_items],
                'last_saved': datetime.now().isoformat()
            }
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Error saving sync state: {e}")
    
    def start_session(self, total_items: int, config_snapshot: Dict = None) -> str:
        """Start a new sync session"""
        session_id = f"sync_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.current_session = SyncSession(
            session_id=session_id,
            start_time=datetime.now().isoformat(),
            config_snapshot=config_snapshot
        )
        
        self.progress = SyncProgress(
            total_items=total_items,
            completed_items=0,
            failed_items=0,
            skipped_items=0,
            start_time=datetime.now().isoformat(),
            last_update=datetime.now().isoformat()
        )
        
        self.current_session.progress = self.progress
        
        # Clear previous state for new session
        self.item_status.clear()
        
        self.save_state()
        logger.info(f"Started sync session: {session_id}")
        
        return session_id
    
    def end_session(self, status: SyncStatus = SyncStatus.COMPLETED):
        """End current sync session"""
        if self.current_session:
            self.current_session.end_time = datetime.now().isoformat()
            self.current_session.status = status.value
            
            if self.progress:
                self.progress.last_update = datetime.now().isoformat()
            
            self.save_state()
            logger.info(f"Ended sync session: {self.current_session.session_id}")
    
    def update_progress(self, current_item: str = None):
        """Update progress tracking"""
        if self.progress:
            if current_item:
                self.progress.current_item = current_item
            
            # Recalculate counts from item_status
            completed = sum(1 for status in self.item_status.values() 
                          if status == SyncStatus.COMPLETED)
            failed = sum(1 for status in self.item_status.values() 
                        if status == SyncStatus.FAILED)
            skipped = sum(1 for status in self.item_status.values() 
                         if status == SyncStatus.SKIPPED)
            
            self.progress.completed_items = completed
            self.progress.failed_items = failed
            self.progress.skipped_items = skipped
            self.progress.last_update = datetime.now().isoformat()
            
            if self.current_session:
                self.current_session.progress = self.progress
    
    def mark_item_status(self, item_path: str, status: SyncStatus):
        """Mark status of a specific item"""
        self.item_status[item_path] = status
        self.update_progress()
        
        logger.debug(f"Marked {item_path} as {status.value}")
    
    def mark_completed(self, item_path: str):
        """Mark item as completed"""
        self.mark_item_status(item_path, SyncStatus.COMPLETED)
    
    def mark_failed(self, item_path: str, error: str, action: str = "sync"):
        """Mark item as failed and add to failed items list"""
        self.mark_item_status(item_path, SyncStatus.FAILED)
        
        # Add or update failed item
        existing_item = next(
            (item for item in self.failed_items if item.file_path == item_path),
            None
        )
        
        if existing_item:
            existing_item.retry_count += 1
            existing_item.last_retry = datetime.now().isoformat()
            existing_item.error = error
        else:
            failed_item = FailedItem(
                file_path=item_path,
                action=action,
                error=error,
                timestamp=datetime.now().isoformat()
            )
            self.failed_items.append(failed_item)
    
    def mark_skipped(self, item_path: str):
        """Mark item as skipped"""
        self.mark_item_status(item_path, SyncStatus.SKIPPED)
    
    def get_failed_items(self) -> List[FailedItem]:
        """Get list of failed items"""
        return self.failed_items.copy()
    
    def get_pending_items(self) -> List[str]:
        """Get list of items that haven't been processed"""
        return [
            path for path, status in self.item_status.items()
            if status == SyncStatus.PENDING
        ]
    
    def get_progress_summary(self) -> Dict[str, Any]:
        """Get progress summary"""
        if not self.progress:
            return {}
        
        return {
            'total_items': self.progress.total_items,
            'completed': self.progress.completed_items,
            'failed': self.progress.failed_items,
            'skipped': self.progress.skipped_items,
            'pending': self.progress.total_items - (
                self.progress.completed_items + 
                self.progress.failed_items + 
                self.progress.skipped_items
            ),
            'completion_percentage': self.progress.completion_percentage,
            'current_item': self.progress.current_item,
            'start_time': self.progress.start_time,
            'last_update': self.progress.last_update
        }
    
    def can_resume(self) -> bool:
        """Check if sync can be resumed"""
        return (
            self.current_session and 
            self.current_session.status == SyncStatus.IN_PROGRESS.value and
            len(self.get_pending_items()) > 0
        )
    
    def clear_failed_items(self):
        """Clear failed items list"""
        self.failed_items.clear()
        self.save_state()
        logger.info("Cleared failed items list")
    
    def _reset_state(self):
        """Reset state to initial values"""
        self.current_session = None
        self.item_status.clear()
        self.failed_items.clear()
        self.progress = None
