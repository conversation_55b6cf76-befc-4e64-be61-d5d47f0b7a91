"""
Google Drive Manager
Handles file and folder operations in Google Drive
"""

import mimetypes
from pathlib import Path
from typing import Op<PERSON>, Tuple
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaFileUpload
from .utils import logger


class GoogleDriveManager:
    """Manages Google Drive operations"""
    
    def __init__(self, drive_service):
        """
        Initialize the drive manager
        
        Args:
            drive_service: Authenticated Google Drive service object
        """
        self.drive_service = drive_service
        self.existing_files = {}  # filename -> file_id mapping
        self.failed_uploads = []
    
    def create_folder(self, name: str, parent_id: str = None) -> Optional[str]:
        """Create a folder in Google Drive"""
        try:
            folder_metadata = {
                'name': name,
                'mimeType': 'application/vnd.google-apps.folder'
            }
            
            if parent_id:
                folder_metadata['parents'] = [parent_id]
            
            folder = self.drive_service.files().create(
                body=folder_metadata,
                fields='id'
            ).execute()
            
            folder_id = folder.get('id')
            logger.info(f"Created folder: {name} (ID: {folder_id})")
            return folder_id
            
        except HttpError as e:
            logger.error(f"Error creating folder {name}: {e}")
            return None
    
    def find_or_create_folder(self, name: str, parent_id: str = None) -> Optional[str]:
        """Find existing folder or create new one"""
        try:
            # Search for existing folder
            query = f"name='{name}' and mimeType='application/vnd.google-apps.folder'"
            if parent_id:
                query += f" and '{parent_id}' in parents"
            
            results = self.drive_service.files().list(
                q=query,
                fields="files(id, name)"
            ).execute()
            
            items = results.get('files', [])
            
            if items:
                folder_id = items[0]['id']
                logger.info(f"Found existing folder: {name} (ID: {folder_id})")
                return folder_id
            else:
                return self.create_folder(name, parent_id)
                
        except HttpError as e:
            logger.error(f"Error finding/creating folder {name}: {e}")
            return None

    def find_existing_file(self, filename: str, folder_id: str) -> Optional[str]:
        """Find existing file in Google Drive folder"""
        try:
            query = f"name='{filename}' and '{folder_id}' in parents and trashed=false"

            results = self.drive_service.files().list(
                q=query,
                fields="files(id, name, modifiedTime)"
            ).execute()

            items = results.get('files', [])

            if items:
                file_info = items[0]  # Get first match
                file_id = file_info['id']
                logger.info(f"Found existing file: {filename} (ID: {file_id})")
                return file_id

            return None

        except HttpError as e:
            logger.error(f"Error searching for file {filename}: {e}")
            return None

    def update_existing_file(self, file_id: str, file_path: Path) -> Optional[Tuple[str, str]]:
        """Update existing file content"""
        try:
            mime_type = mimetypes.guess_type(str(file_path))[0] or 'application/octet-stream'
            media = MediaFileUpload(str(file_path), mimetype=mime_type)

            # Update file content
            file = self.drive_service.files().update(
                fileId=file_id,
                media_body=media,
                fields='id,webViewLink'
            ).execute()

            # Ensure file is publicly viewable
            permission = {
                'type': 'anyone',
                'role': 'reader'
            }
            self.drive_service.permissions().create(
                fileId=file_id,
                body=permission
            ).execute()

            # Get public URL for embedding
            public_url = f"https://drive.google.com/uc?id={file_id}"

            logger.info(f"Updated existing file: {file_path.name} (ID: {file_id})")
            return file_id, public_url

        except HttpError as e:
            logger.error(f"Error updating file {file_path.name}: {e}")
            return None

    def upload_media_file(self, file_path: Path, folder_id: str) -> Optional[Tuple[str, str]]:
        """Upload or update a media file to Google Drive and return file_id and public URL"""
        try:
            filename = file_path.name

            # Check if file already exists
            existing_file_id = self.find_existing_file(filename, folder_id)

            if existing_file_id:
                # Update existing file
                logger.info(f"File {filename} already exists, updating...")
                result = self.update_existing_file(existing_file_id, file_path)
                if result:
                    return result
                else:
                    # If update fails, add to failed list and try upload new
                    self.failed_uploads.append({
                        'file_path': str(file_path),
                        'action': 'update',
                        'error': 'Failed to update existing file'
                    })
                    logger.warning(f"Failed to update {filename}, attempting new upload...")

            # Upload new file
            file_metadata = {
                'name': filename,
                'parents': [folder_id]
            }

            mime_type = mimetypes.guess_type(str(file_path))[0] or 'application/octet-stream'
            media = MediaFileUpload(str(file_path), mimetype=mime_type)

            file = self.drive_service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id,webViewLink'
            ).execute()

            file_id = file.get('id')

            # Make file publicly viewable
            permission = {
                'type': 'anyone',
                'role': 'reader'
            }
            self.drive_service.permissions().create(
                fileId=file_id,
                body=permission
            ).execute()

            # Get public URL for embedding
            public_url = f"https://drive.google.com/uc?id={file_id}"

            # Cache the file info
            self.existing_files[filename] = file_id

            logger.info(f"Uploaded new media file: {filename} (ID: {file_id})")
            return file_id, public_url

        except HttpError as e:
            logger.error(f"Error uploading media file {file_path}: {e}")
            # Add to failed uploads list
            self.failed_uploads.append({
                'file_path': str(file_path),
                'action': 'upload',
                'error': str(e)
            })
            return None
