"""
Link Manager
Handles internal link validation and automatic creation of missing target Google Docs
"""

import re
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass
from datetime import datetime
from .utils import logger


@dataclass
class LinkValidation:
    """Result of link validation"""
    link_text: str
    target: str
    exists: bool
    target_path: Optional[Path] = None
    doc_id: Optional[str] = None
    error: Optional[str] = None


@dataclass
class InternalLink:
    """Represents an internal link in a note"""
    source_note: str
    target: str
    alias: Optional[str] = None
    line_number: int = 0
    position: int = 0


class LinkRegistry:
    """Registry to track all internal links across notes"""
    
    def __init__(self, registry_file: str = "link_registry.json"):
        self.registry_file = registry_file
        self.links: Dict[str, List[InternalLink]] = {}
        self.reverse_links: Dict[str, List[str]] = {}  # target -> sources
        self.doc_mapping: Dict[str, str] = {}  # note_name -> doc_id
        self.load_registry()
    
    def load_registry(self):
        """Load link registry from file"""
        try:
            if Path(self.registry_file).exists():
                with open(self.registry_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # Convert dict data back to InternalLink objects
                for source, links_data in data.get('links', {}).items():
                    self.links[source] = [
                        InternalLink(**link_data) for link_data in links_data
                    ]
                
                self.reverse_links = data.get('reverse_links', {})
                self.doc_mapping = data.get('doc_mapping', {})
                
                logger.info(f"Loaded link registry with {len(self.links)} source notes")
        except Exception as e:
            logger.warning(f"Could not load link registry: {e}")
            self.links = {}
            self.reverse_links = {}
            self.doc_mapping = {}
    
    def save_registry(self):
        """Save link registry to file"""
        try:
            # Convert InternalLink objects to dict for JSON serialization
            links_data = {}
            for source, links in self.links.items():
                links_data[source] = [
                    {
                        'source_note': link.source_note,
                        'target': link.target,
                        'alias': link.alias,
                        'line_number': link.line_number,
                        'position': link.position
                    }
                    for link in links
                ]
            
            data = {
                'links': links_data,
                'reverse_links': self.reverse_links,
                'doc_mapping': self.doc_mapping,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.registry_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Could not save link registry: {e}")
    
    def add_links(self, source_note: str, links: List[InternalLink]):
        """Add links from a source note"""
        self.links[source_note] = links
        
        # Update reverse links
        for link in links:
            if link.target not in self.reverse_links:
                self.reverse_links[link.target] = []
            if source_note not in self.reverse_links[link.target]:
                self.reverse_links[link.target].append(source_note)
    
    def get_missing_targets(self) -> Set[str]:
        """Get all link targets that don't have corresponding notes"""
        all_targets = set()
        for links in self.links.values():
            for link in links:
                all_targets.add(link.target)
        
        existing_notes = set(self.links.keys())
        return all_targets - existing_notes
    
    def update_doc_mapping(self, note_name: str, doc_id: str):
        """Update mapping of note name to Google Doc ID"""
        self.doc_mapping[note_name] = doc_id


class LinkManager:
    """Manages internal links and creates missing target documents"""
    
    def __init__(self, file_manager, docs_manager, drive_manager):
        """
        Initialize link manager
        
        Args:
            file_manager: FileManager instance
            docs_manager: GoogleDocsManager instance
            drive_manager: GoogleDriveManager instance
        """
        self.file_manager = file_manager
        self.docs_manager = docs_manager
        self.drive_manager = drive_manager
        self.registry = LinkRegistry()
        
        # Patterns for different link types
        self.internal_link_pattern = r'\[\[([^\]|]+)(\|([^\]]+))?\]\]'
        self.external_link_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
    
    def extract_internal_links(self, content: str, source_note: str) -> List[InternalLink]:
        """Extract all internal links from markdown content"""
        links = []
        
        for line_num, line in enumerate(content.split('\n'), 1):
            for match in re.finditer(self.internal_link_pattern, line):
                target = match.group(1).strip()
                alias = match.group(3).strip() if match.group(3) else None
                position = match.start()
                
                link = InternalLink(
                    source_note=source_note,
                    target=target,
                    alias=alias,
                    line_number=line_num,
                    position=position
                )
                links.append(link)
        
        return links
    
    def validate_internal_links(self, content: str, source_note: str) -> List[LinkValidation]:
        """Validate all internal links in content"""
        links = self.extract_internal_links(content, source_note)
        validations = []
        
        for link in links:
            validation = self._validate_single_link(link)
            validations.append(validation)
        
        # Update registry with found links
        self.registry.add_links(source_note, links)
        
        return validations
    
    def _validate_single_link(self, link: InternalLink) -> LinkValidation:
        """Validate a single internal link"""
        target_name = link.target
        
        # Check if target exists as a note file
        target_path = self._find_target_file(target_name)
        
        if target_path and target_path.exists():
            return LinkValidation(
                link_text=link.alias or link.target,
                target=link.target,
                exists=True,
                target_path=target_path,
                doc_id=self.registry.doc_mapping.get(target_name)
            )
        else:
            return LinkValidation(
                link_text=link.alias or link.target,
                target=link.target,
                exists=False,
                error=f"Target file not found: {target_name}"
            )
    
    def _find_target_file(self, target_name: str) -> Optional[Path]:
        """Find target file in Obsidian vault"""
        vault_path = self.file_manager.obsidian_path
        
        # Try different variations of the target name
        possible_names = [
            f"{target_name}.md",
            target_name,
            f"{target_name}.txt"
        ]
        
        for name in possible_names:
            # Search in vault root and subdirectories
            for md_file in vault_path.rglob(name):
                if md_file.is_file():
                    return md_file
        
        return None
    
    def create_missing_target_docs(self, missing_links: List[LinkValidation], 
                                 folder_id: str) -> Dict[str, str]:
        """
        Create Google Docs for missing link targets
        
        Args:
            missing_links: List of missing link validations
            folder_id: Google Drive folder ID to create docs in
            
        Returns:
            Dict mapping target names to created doc IDs
        """
        created_docs = {}
        
        for link_validation in missing_links:
            if link_validation.exists:
                continue
                
            target_name = link_validation.target
            
            # Skip if already created in this session
            if target_name in created_docs:
                continue
            
            try:
                # Create a basic Google Doc for the missing target
                doc_title = target_name
                placeholder_content = [
                    {
                        'insertText': {
                            'location': {'index': 1},
                            'text': f"# {target_name}\n\nThis note was automatically created because it was referenced from another note.\n\nYou can edit this content in Google Docs.\n"
                        }
                    }
                ]
                
                doc_id = self.docs_manager.create_google_doc(
                    doc_title, placeholder_content, folder_id
                )
                
                if doc_id:
                    created_docs[target_name] = doc_id
                    self.registry.update_doc_mapping(target_name, doc_id)
                    logger.info(f"Created missing target doc: {target_name} (ID: {doc_id})")
                else:
                    logger.error(f"Failed to create doc for missing target: {target_name}")
                    
            except Exception as e:
                logger.error(f"Error creating doc for {target_name}: {e}")
        
        # Save updated registry
        self.registry.save_registry()
        
        return created_docs
    
    def update_link_registry(self, note_path: Path, doc_id: str):
        """Update registry when a note is successfully synced"""
        note_name = note_path.stem
        self.registry.update_doc_mapping(note_name, doc_id)
        self.registry.save_registry()
    
    def resolve_link_target(self, link_target: str) -> Optional[str]:
        """Resolve link target to Google Doc ID if available"""
        return self.registry.doc_mapping.get(link_target)
    
    def get_backlinks(self, note_name: str) -> List[str]:
        """Get all notes that link to the given note"""
        return self.registry.reverse_links.get(note_name, [])
    
    def get_link_statistics(self) -> Dict[str, int]:
        """Get statistics about links in the vault"""
        total_links = sum(len(links) for links in self.registry.links.values())
        missing_targets = len(self.registry.get_missing_targets())
        
        return {
            "total_notes_with_links": len(self.registry.links),
            "total_internal_links": total_links,
            "missing_targets": missing_targets,
            "mapped_docs": len(self.registry.doc_mapping)
        }
