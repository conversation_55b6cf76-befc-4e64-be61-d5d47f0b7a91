"""
Error Handler
Centralized error handling with categorization, retry mechanisms, and circuit breaker patterns
"""

import time
import json
from enum import Enum
from typing import Optional, Dict, Any, List, Callable
from datetime import datetime, timedelta
from googleapiclient.errors import HttpError
from .utils import logger


class ErrorType(Enum):
    """Error categories for different handling strategies"""
    NETWORK_ERROR = "network"
    API_QUOTA_ERROR = "quota"
    API_PERMISSION_ERROR = "permission"
    VALIDATION_ERROR = "validation"
    FILE_NOT_FOUND = "file_not_found"
    AUTHENTICATION_ERROR = "auth"
    UNKNOWN_ERROR = "unknown"


class RetryConfig:
    """Configuration for retry behavior"""
    def __init__(self, max_attempts: int = 3, base_delay: float = 1.0, max_delay: float = 60.0):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay


class CircuitBreaker:
    """Circuit breaker to prevent cascading failures"""
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def can_execute(self) -> bool:
        """Check if operation can be executed"""
        if self.state == "CLOSED":
            return True
        elif self.state == "OPEN":
            if self.last_failure_time and \
               datetime.now() - self.last_failure_time > timedelta(seconds=self.recovery_timeout):
                self.state = "HALF_OPEN"
                return True
            return False
        else:  # HALF_OPEN
            return True
    
    def record_success(self):
        """Record successful operation"""
        self.failure_count = 0
        self.state = "CLOSED"
    
    def record_failure(self):
        """Record failed operation"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"


class ErrorHandler:
    """Centralized error handling with retry and circuit breaker capabilities"""
    
    def __init__(self, retry_config: Optional[RetryConfig] = None):
        """
        Initialize error handler
        
        Args:
            retry_config: Configuration for retry behavior
        """
        self.retry_config = retry_config or RetryConfig()
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.error_stats = {
            "total_errors": 0,
            "errors_by_type": {},
            "retries_attempted": 0,
            "circuit_breaker_trips": 0
        }
    
    def categorize_error(self, exception: Exception) -> ErrorType:
        """Categorize error for appropriate handling"""
        if isinstance(exception, HttpError):
            status_code = exception.resp.status
            
            if status_code == 403:
                error_details = str(exception)
                if "quota" in error_details.lower() or "rate" in error_details.lower():
                    return ErrorType.API_QUOTA_ERROR
                else:
                    return ErrorType.API_PERMISSION_ERROR
            elif status_code == 401:
                return ErrorType.AUTHENTICATION_ERROR
            elif status_code == 404:
                return ErrorType.FILE_NOT_FOUND
            elif status_code >= 500:
                return ErrorType.NETWORK_ERROR
            else:
                return ErrorType.VALIDATION_ERROR
        
        elif isinstance(exception, (ConnectionError, TimeoutError)):
            return ErrorType.NETWORK_ERROR
        
        elif isinstance(exception, (ValueError, TypeError)):
            return ErrorType.VALIDATION_ERROR
        
        else:
            return ErrorType.UNKNOWN_ERROR
    
    def should_retry(self, error_type: ErrorType, attempt_count: int) -> bool:
        """Determine if operation should be retried"""
        if attempt_count >= self.retry_config.max_attempts:
            return False
        
        # Don't retry certain error types
        non_retryable = {
            ErrorType.API_PERMISSION_ERROR,
            ErrorType.AUTHENTICATION_ERROR,
            ErrorType.VALIDATION_ERROR
        }
        
        return error_type not in non_retryable
    
    def get_retry_delay(self, attempt_count: int) -> float:
        """Calculate retry delay with exponential backoff"""
        delay = self.retry_config.base_delay * (2 ** attempt_count)
        return min(delay, self.retry_config.max_delay)
    
    def get_circuit_breaker(self, operation_name: str) -> CircuitBreaker:
        """Get or create circuit breaker for operation"""
        if operation_name not in self.circuit_breakers:
            self.circuit_breakers[operation_name] = CircuitBreaker()
        return self.circuit_breakers[operation_name]
    
    def execute_with_retry(self, operation: Callable, operation_name: str, 
                          context: Dict[str, Any] = None) -> Any:
        """
        Execute operation with retry logic and circuit breaker
        
        Args:
            operation: Function to execute
            operation_name: Name for circuit breaker and logging
            context: Additional context for error logging
            
        Returns:
            Result of successful operation
            
        Raises:
            Exception: If all retries fail
        """
        circuit_breaker = self.get_circuit_breaker(operation_name)
        context = context or {}
        
        if not circuit_breaker.can_execute():
            logger.error(f"Circuit breaker OPEN for {operation_name}")
            self.error_stats["circuit_breaker_trips"] += 1
            raise Exception(f"Circuit breaker is OPEN for {operation_name}")
        
        last_exception = None
        
        for attempt in range(self.retry_config.max_attempts):
            try:
                result = operation()
                circuit_breaker.record_success()
                
                if attempt > 0:
                    logger.info(f"Operation {operation_name} succeeded on attempt {attempt + 1}")
                
                return result
                
            except Exception as e:
                last_exception = e
                error_type = self.categorize_error(e)
                
                self.error_stats["total_errors"] += 1
                self.error_stats["errors_by_type"][error_type.value] = \
                    self.error_stats["errors_by_type"].get(error_type.value, 0) + 1
                
                self.log_error(e, operation_name, attempt + 1, context)
                
                if not self.should_retry(error_type, attempt + 1):
                    logger.error(f"Not retrying {operation_name} due to error type: {error_type.value}")
                    circuit_breaker.record_failure()
                    break
                
                if attempt < self.retry_config.max_attempts - 1:
                    delay = self.get_retry_delay(attempt)
                    logger.info(f"Retrying {operation_name} in {delay:.1f} seconds...")
                    self.error_stats["retries_attempted"] += 1
                    time.sleep(delay)
        
        # All retries failed
        circuit_breaker.record_failure()
        logger.error(f"All retries failed for {operation_name}")
        raise last_exception
    
    def log_error(self, exception: Exception, operation_name: str, 
                  attempt: int, context: Dict[str, Any]):
        """Log error with context information"""
        error_type = self.categorize_error(exception)
        
        log_data = {
            "operation": operation_name,
            "attempt": attempt,
            "error_type": error_type.value,
            "error_message": str(exception),
            "context": context,
            "timestamp": datetime.now().isoformat()
        }
        
        logger.error(f"Error in {operation_name} (attempt {attempt}): {exception}")
        logger.debug(f"Error details: {json.dumps(log_data, indent=2)}")
    
    def create_error_report(self) -> Dict[str, Any]:
        """Create comprehensive error report"""
        return {
            "error_statistics": self.error_stats.copy(),
            "circuit_breaker_states": {
                name: {
                    "state": cb.state,
                    "failure_count": cb.failure_count,
                    "last_failure": cb.last_failure_time.isoformat() if cb.last_failure_time else None
                }
                for name, cb in self.circuit_breakers.items()
            },
            "generated_at": datetime.now().isoformat()
        }
    
    def reset_circuit_breakers(self):
        """Reset all circuit breakers"""
        for cb in self.circuit_breakers.values():
            cb.failure_count = 0
            cb.state = "CLOSED"
            cb.last_failure_time = None
        logger.info("All circuit breakers reset")
