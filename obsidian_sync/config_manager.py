"""
Configuration Manager
Centralized configuration management for the sync tool
"""

import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from .utils import logger


@dataclass
class RetryConfig:
    """Configuration for retry behavior"""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_backoff: bool = True


@dataclass
class FormattingConfig:
    """Configuration for formatting behavior"""
    enable_bold: bool = True
    enable_italic: bool = True
    enable_code: bool = True
    enable_strikethrough: bool = True
    fallback_to_plain_text: bool = True
    code_font_family: str = "Courier New"
    code_font_size: int = 10


@dataclass
class LinkConfig:
    """Configuration for link handling"""
    create_missing_targets: bool = True
    validate_internal_links: bool = True
    auto_detect_language: bool = True
    preserve_link_registry: bool = True


@dataclass
class SyncConfig:
    """Configuration for sync behavior"""
    batch_size: int = 10
    enable_progress_tracking: bool = True
    save_state_frequency: int = 5  # Save state every N operations
    enable_resumable_sync: bool = True
    max_concurrent_uploads: int = 3


@dataclass
class LoggingConfig:
    """Configuration for logging"""
    log_level: str = "INFO"
    log_file: str = "google_sync.log"
    enable_file_logging: bool = True
    enable_console_logging: bool = True
    max_log_size_mb: int = 10


@dataclass
class Config:
    """Main configuration class"""
    retry: RetryConfig
    formatting: FormattingConfig
    links: LinkConfig
    sync: SyncConfig
    logging: LoggingConfig
    
    def __init__(self):
        self.retry = RetryConfig()
        self.formatting = FormattingConfig()
        self.links = LinkConfig()
        self.sync = SyncConfig()
        self.logging = LoggingConfig()


class ConfigManager:
    """Manages configuration loading, saving, and validation"""
    
    def __init__(self, config_file: str = "sync_config.json"):
        """
        Initialize configuration manager
        
        Args:
            config_file: Path to configuration file
        """
        self.config_file = Path(config_file)
        self.config = Config()
        self.load_config()
    
    def load_config(self) -> Config:
        """Load configuration from file"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Load each configuration section
                if 'retry' in data:
                    self.config.retry = RetryConfig(**data['retry'])
                
                if 'formatting' in data:
                    self.config.formatting = FormattingConfig(**data['formatting'])
                
                if 'links' in data:
                    self.config.links = LinkConfig(**data['links'])
                
                if 'sync' in data:
                    self.config.sync = SyncConfig(**data['sync'])
                
                if 'logging' in data:
                    self.config.logging = LoggingConfig(**data['logging'])
                
                logger.info(f"Loaded configuration from {self.config_file}")
            else:
                logger.info("No configuration file found, using defaults")
                self.save_config()  # Save default configuration
                
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            logger.info("Using default configuration")
            self.config = Config()
        
        return self.config
    
    def save_config(self) -> bool:
        """Save current configuration to file"""
        try:
            config_data = {
                'retry': asdict(self.config.retry),
                'formatting': asdict(self.config.formatting),
                'links': asdict(self.config.links),
                'sync': asdict(self.config.sync),
                'logging': asdict(self.config.logging)
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2)
            
            logger.info(f"Saved configuration to {self.config_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            return False
    
    def get_retry_config(self) -> RetryConfig:
        """Get retry configuration"""
        return self.config.retry
    
    def get_formatting_config(self) -> FormattingConfig:
        """Get formatting configuration"""
        return self.config.formatting
    
    def get_link_config(self) -> LinkConfig:
        """Get link configuration"""
        return self.config.links
    
    def get_sync_config(self) -> SyncConfig:
        """Get sync configuration"""
        return self.config.sync
    
    def get_logging_config(self) -> LoggingConfig:
        """Get logging configuration"""
        return self.config.logging
    
    def update_config(self, section: str, **kwargs) -> bool:
        """
        Update configuration section
        
        Args:
            section: Configuration section name
            **kwargs: Configuration values to update
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if section == 'retry':
                for key, value in kwargs.items():
                    if hasattr(self.config.retry, key):
                        setattr(self.config.retry, key, value)
            
            elif section == 'formatting':
                for key, value in kwargs.items():
                    if hasattr(self.config.formatting, key):
                        setattr(self.config.formatting, key, value)
            
            elif section == 'links':
                for key, value in kwargs.items():
                    if hasattr(self.config.links, key):
                        setattr(self.config.links, key, value)
            
            elif section == 'sync':
                for key, value in kwargs.items():
                    if hasattr(self.config.sync, key):
                        setattr(self.config.sync, key, value)
            
            elif section == 'logging':
                for key, value in kwargs.items():
                    if hasattr(self.config.logging, key):
                        setattr(self.config.logging, key, value)
            
            else:
                logger.error(f"Unknown configuration section: {section}")
                return False
            
            return self.save_config()
            
        except Exception as e:
            logger.error(f"Error updating configuration: {e}")
            return False
    
    def validate_config(self) -> bool:
        """Validate current configuration"""
        try:
            # Validate retry config
            if self.config.retry.max_attempts < 1:
                logger.error("max_attempts must be at least 1")
                return False
            
            if self.config.retry.base_delay < 0:
                logger.error("base_delay must be non-negative")
                return False
            
            # Validate sync config
            if self.config.sync.batch_size < 1:
                logger.error("batch_size must be at least 1")
                return False
            
            if self.config.sync.max_concurrent_uploads < 1:
                logger.error("max_concurrent_uploads must be at least 1")
                return False
            
            # Validate logging config
            valid_log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            if self.config.logging.log_level not in valid_log_levels:
                logger.error(f"log_level must be one of: {valid_log_levels}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating configuration: {e}")
            return False
    
    def create_default_config_file(self, file_path: str) -> bool:
        """Create a default configuration file with comments"""
        try:
            config_template = {
                "_comment": "Obsidian to Google Drive Sync Configuration",
                "retry": {
                    "_comment": "Retry configuration for failed operations",
                    "max_attempts": 3,
                    "base_delay": 1.0,
                    "max_delay": 60.0,
                    "exponential_backoff": True
                },
                "formatting": {
                    "_comment": "Text formatting configuration",
                    "enable_bold": True,
                    "enable_italic": True,
                    "enable_code": True,
                    "enable_strikethrough": True,
                    "fallback_to_plain_text": True,
                    "code_font_family": "Courier New",
                    "code_font_size": 10
                },
                "links": {
                    "_comment": "Link handling configuration",
                    "create_missing_targets": True,
                    "validate_internal_links": True,
                    "auto_detect_language": True,
                    "preserve_link_registry": True
                },
                "sync": {
                    "_comment": "Synchronization behavior configuration",
                    "batch_size": 10,
                    "enable_progress_tracking": True,
                    "save_state_frequency": 5,
                    "enable_resumable_sync": True,
                    "max_concurrent_uploads": 3
                },
                "logging": {
                    "_comment": "Logging configuration",
                    "log_level": "INFO",
                    "log_file": "google_sync.log",
                    "enable_file_logging": True,
                    "enable_console_logging": True,
                    "max_log_size_mb": 10
                }
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_template, f, indent=2)
            
            logger.info(f"Created default configuration file: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating default configuration file: {e}")
            return False
