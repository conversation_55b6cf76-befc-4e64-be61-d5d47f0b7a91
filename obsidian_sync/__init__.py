"""
Obsidian to Google Docs/Drive Sync Package
Synchronizes notes and media from Obsidian vault to Google Docs and Google Drive
Enhanced with robust error handling, link management, and formatting support
"""

from .sync import ObsidianToGoogleSync
from .utils import grapheme_len, clean_whitespace_lines
from .error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorType, RetryConfig
from .link_manager import LinkManager, LinkValidation
from .config_manager import ConfigManager, Config
from .sync_state import SyncState, SyncStatus
from .formatting_processor import FormattingProcessor
from .codeblock_processor import CodeblockProcessor

__version__ = "2.0.0"
__all__ = [
    "ObsidianToGoogleSync",
    "grapheme_len",
    "clean_whitespace_lines",
    "ErrorHandler",
    "ErrorType",
    "RetryConfig",
    "LinkManager",
    "LinkValidation",
    "ConfigManager",
    "Config",
    "SyncState",
    "SyncStatus",
    "FormattingProcessor",
    "CodeblockProcessor"
]
