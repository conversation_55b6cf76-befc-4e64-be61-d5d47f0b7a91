# Obsidian Sync Tools

Công cụ Python để đồng bộ notes và media từ Obsidian vault sang các nền tảng khác nhau như Microsoft OneNote và Google Docs/Drive.

## 🎉 Cập nhật mới: Modular Architecture

Dự án đã được tái cấu trúc thành **modular architecture** để dễ maintain và mở rộng:

```
obsidian_sync/           # 🆕 Main sync module
├── auth.py             # Google authentication
├── drive_manager.py    # Google Drive operations
├── docs_manager.py     # Google Docs operations
├── markdown_processor.py # Markdown conversion
├── file_manager.py     # File operations
├── sync.py            # Main coordinator
└── utils.py           # Utilities

main.py                 # 🆕 New entry point
obsidian_to_google_sync.py # Legacy file (still works)
```

## Tính năng

### Obsidian to OneNote Sync

- ✅ Đồng bộ tất cả markdown files từ Obsidian
- ✅ Chuyển đổi Obsidian markdown syntax sang HTML cho OneNote
- ✅ Hỗ trợ embedded images và media files
- ✅ Tự động tạo notebook và sections dựa trên cấu trúc thư mục
- ✅ Xử lý Obsidian links `[[link]]`
- ✅ Hỗ trợ tables, code blocks, và formatting

### Obsidian to Google Docs/Drive Sync

- ✅ Đồng bộ tất cả markdown files từ Obsidian
- ✅ Chuyển đổi Obsidian markdown sang Google Docs format
- ✅ Upload media files lên Google Drive và chèn trực tiếp vào Google Docs
- ✅ Tự động tạo folder structure trên Google Drive
- ✅ Hỗ trợ headers, lists, tables, code blocks
- ✅ **🆕 Link Detection & Conversion**: Tự động phát hiện và convert links
  - External links: `[text](url)` → Clickable links với URL
  - Internal Obsidian links: `[[link]]` → Formatted text links
  - Internal links với alias: `[[link|alias]]` → Display alias text
- ✅ OAuth2 authentication với Google
- ✅ Smart file management: replace file đã tồn tại, upload mới nếu chưa có
- ✅ Failed uploads tracking và retry functionality
- ✅ **Selective sync với allowed.list**: Chỉ sync các file được chỉ định trong allowed.list
- ✅ **Modular architecture**: Dễ maintain và test từng component

## Yêu cầu hệ thống

- Python 3.7+
- Microsoft Azure App Registration (cho OneNote sync)
- Google Cloud Project với APIs được kích hoạt (cho Google sync)
- Obsidian vault với markdown files

## Cài đặt

1. Clone repository:

```bash
git clone <repository-url>
cd obsidian-2-google-drive
```

2. Cài đặt dependencies:

```bash
pip3 install -r requirements.txt
```

## Sử dụng

### OneNote Sync

1. Tạo config file:

```bash
python obsidian_to_onenote_sync.py --create-config
```

2. Chỉnh sửa `sync_config.json` với Microsoft App credentials

3. Chạy sync:

```bash
python obsidian_to_onenote_sync.py
# hoặc
make -f sync.mk sync
# hoặc
./run_sync.sh
```

### Google Docs/Drive Sync

#### Cách mới (Khuyến khích) - Sử dụng Modular Architecture:

```bash
# Tạo config file
python main.py --create-config

# Chạy sync
python main.py --obsidian-path ./obsidian --credentials credentials.json

# Retry failed uploads
python main.py --retry-failed
```

#### Cách cũ (Vẫn hoạt động):

```bash
# Tạo config file
python obsidian_to_google_sync.py --create-config

# Chạy sync
python obsidian_to_google_sync.py
# hoặc
make -f sync.mk sync-google
# hoặc
./run_google_sync.sh
```

## 🔗 Link Detection & Conversion

Công cụ hỗ trợ tự động phát hiện và convert các loại links trong Obsidian notes:

### External Links

```markdown
[Google](https://www.google.com)
[GitHub](https://github.com)
```

**Kết quả**: Tạo clickable links trong Google Docs với URL tương ứng

### Internal Obsidian Links

```markdown
[[My Note]] # Link đến note khác
[[Another Note|Custom Text]] # Link với alias
```

**Kết quả**: Hiển thị text với formatting đặc biệt (màu xanh, underline)

### Mixed Content

```markdown
Tham khảo [[Programming Tips|tips]] và [Stack Overflow](https://stackoverflow.com) để học thêm.
```

**Kết quả**: Cả internal và external links được format đúng trong cùng paragraph

### Edge Cases Được Hỗ Trợ

- Links có ký tự đặc biệt: `[Example & Test](https://example.com/path?param=value)`
- Internal links có spaces: `[[File with spaces|Display Name]]`
- Multiple links trong cùng paragraph

### Programmatic Usage (Cách mới)

```python
from obsidian_sync import ObsidianToGoogleSync

# Initialize
sync = ObsidianToGoogleSync("./obsidian", "credentials.json")

# Run sync
success = sync.sync_notes("My Obsidian Backup")

if success:
    print("✅ Sync completed!")
else:
    print("❌ Sync failed!")
```

## Makefile Commands

Sử dụng Makefile để thực hiện các tác vụ phổ biến:

```bash
make -f sync.mk help          # Hiển thị help
make -f sync.mk install       # Cài đặt dependencies
make -f sync.mk setup-example # Tạo Obsidian vault mẫu
make -f sync.mk create-config # Tạo config file
make -f sync.mk sync          # Chạy OneNote sync
make -f sync.mk sync-google   # Chạy Google Docs/Drive sync
make -f sync.mk clean         # Dọn dẹp files tạm
```

## Cấu trúc dự án

```
sync/
├── obsidian_to_onenote_sync.py    # OneNote sync module
├── obsidian_to_google_sync.py     # Google Docs/Drive sync module
├── requirements.txt               # Python dependencies
├── sync.mk                        # Makefile commands
├── run_sync.sh                    # OneNote sync runner script
├── run_google_sync.sh             # Google sync runner script
├── OBSIDIAN_SYNC_README.md        # OneNote sync documentation
├── GOOGLE_SYNC_README.md          # Google sync documentation
└── README.md                      # File này
```

## Logs và Troubleshooting

- OneNote sync: `sync.log`
- Google sync: `google_sync.log`

Xem các file README chi tiết:

- [OneNote Sync Guide](docs/OBSIDIAN_SYNC_README.md)
- [Google Docs/Drive Sync Guide](docs/GOOGLE_SYNC_README.md)

## Ví dụ sử dụng

### Tạo Obsidian vault mẫu

```bash
make -f sync.mk setup-example
```

### Sync toàn bộ vault

```bash
# OneNote
./run_sync.sh

# Google Docs/Drive
./run_google_sync.sh
```

## Hỗ trợ format

### Obsidian Markdown Features

- Headers (`# ## ###`)
- Bold và italic (`**bold**`, `*italic*`)
- Links (`[[internal link]]`, `[external](url)`)
- Images (`![[image.png]]`)
- Code blocks (`code`)
- Tables
- Lists (bullet và numbered)
- Embedded media files

### Platform-specific Features

**OneNote:**

- Tạo notebooks và sections
- HTML formatting
- Embedded images as base64

**Google Docs/Drive:**

- Folder structure mirroring
- Native Google Docs formatting
- Media files uploaded to Drive và chèn trực tiếp vào documents
- OAuth2 authentication
- Tự động tìm và xử lý hình ảnh từ các thư mục attachments

## Contributing

1. Fork repository
2. Tạo feature branch: `git checkout -b feature-name`
3. Thực hiện thay đổi
4. Test các thay đổi
5. Commit: `git commit -m "Add feature"`
6. Push: `git push origin feature-name`
7. Tạo pull request

## License

MIT License - xem file LICENSE để biết chi tiết.
