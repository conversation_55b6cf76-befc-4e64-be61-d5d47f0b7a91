# allowed.list - File filtering for Obsidian to Google Drive sync
# Add one filename per line (with or without .md extension)
# Lines starting with # are comments and will be ignored
# Empty lines are ignored
# If this file is empty or doesn't exist, all files will be synced

# Example usage:
# 1. To sync only specific files, uncomment and modify the lines below:

# My Important Note.md
# Project Documentation
# Meeting Notes 2024
# Quick Ideas

# 2. You can use filenames with or without the .md extension:
# Both "Note.md" and "Note" will match "Note.md"

# 3. File matching is exact - no wildcards or patterns supported
Working.md
Post score algorithm - Trending algorithm.md
