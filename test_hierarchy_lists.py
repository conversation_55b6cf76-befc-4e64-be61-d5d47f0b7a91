#!/usr/bin/env python3
"""
Test script for nested lists functionality
"""

import sys
import os

# Add the obsidian_sync directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'obsidian_sync'))

from obsidian_sync.markdown_processor import MarkdownProcessor
from obsidian_sync.file_manager import FileManager
from pathlib import Path

def test_nested_lists():
    """Test the nested lists functionality"""
    
    # Create a file manager instance
    file_manager = FileManager("./obsidian")
    
    # Create a markdown processor instance
    processor = MarkdownProcessor(file_manager)
    
    # Test files to check
    test_files = ["test_nested_lists.md", "test_edge_cases.md"]
    
    for test_file_name in test_files:
        test_file = Path(test_file_name)
        if not test_file.exists():
            print(f"❌ Test file not found: {test_file_name}")
            continue
        
        with open(test_file, 'r', encoding='utf-8') as f:
            markdown_content = f.read()
        
        print(f"📄 Testing {test_file_name}...")
        print("=" * 50)
        
        # Test the conversion
        try:
            requests = processor.convert_markdown_to_docs_requests(markdown_content, test_file)
            
            print(f"✅ Successfully generated {len(requests)} Google Docs requests")
            
            # Analyze list-related requests
            list_requests = [req for req in requests if 'createParagraphBullets' in req]
            indent_requests = [req for req in requests if 'updateParagraphStyle' in req and 'indentFirstLine' in req.get('updateParagraphStyle', {}).get('paragraphStyle', {})]
            
            print(f"📊 List analysis:")
            print(f"  - Total list items: {len(list_requests)}")
            print(f"  - Indented items: {len(indent_requests)}")
            
            # Show indentation levels
            indent_levels = {}
            for req in indent_requests:
                magnitude = req['updateParagraphStyle']['paragraphStyle']['indentFirstLine']['magnitude']
                level = magnitude // 36  # 36 PT per level
                indent_levels[level] = indent_levels.get(level, 0) + 1
            
            if indent_levels:
                print(f"  - Indentation levels:")
                for level, count in sorted(indent_levels.items()):
                    print(f"    Level {level}: {count} items")
            
            # Show bullet presets used
            bullet_presets = {}
            for req in list_requests:
                preset = req['createParagraphBullets']['bulletPreset']
                bullet_presets[preset] = bullet_presets.get(preset, 0) + 1
            
            print(f"  - Bullet presets used:")
            for preset, count in bullet_presets.items():
                print(f"    {preset}: {count} items")
                
        except Exception as e:
            print(f"❌ Error during conversion: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n")

if __name__ == "__main__":
    test_nested_lists()
