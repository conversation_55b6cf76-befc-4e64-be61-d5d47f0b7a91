{"current_session": {"session_id": "sync_20250724_082140", "start_time": "2025-07-24T08:21:40.485593", "end_time": "2025-07-24T08:22:02.526084", "status": "completed", "progress": {"total_items": 2, "completed_items": 2, "failed_items": 0, "skipped_items": 0, "current_item": "obsidian/Working.md", "start_time": "2025-07-24T08:21:40.485607", "last_update": "2025-07-24T08:22:02.526114"}, "config_snapshot": {"root_folder": "Obsidian Sync", "total_notes": 2, "config": {"retry": {"max_attempts": 3, "base_delay": 1.0, "max_delay": 60.0, "exponential_backoff": true}, "formatting": {"enable_bold": true, "enable_italic": true, "enable_code": true, "enable_strikethrough": true, "fallback_to_plain_text": true, "code_font_family": "Courier New", "code_font_size": 10}, "links": {"create_missing_targets": true, "validate_internal_links": true, "auto_detect_language": true, "preserve_link_registry": true}, "sync": {"batch_size": 10, "enable_progress_tracking": true, "save_state_frequency": 5, "enable_resumable_sync": true, "max_concurrent_uploads": 3}, "logging": {"log_level": "INFO", "log_file": "google_sync.log", "enable_file_logging": true, "enable_console_logging": true, "max_log_size_mb": 10}}}}, "item_status": {"obsidian/Post score algorithm - Trending algorithm.md": "completed", "obsidian/Working.md": "completed"}, "failed_items": [], "last_saved": "2025-07-24T08:22:02.526379"}