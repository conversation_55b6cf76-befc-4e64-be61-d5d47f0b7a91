#!/usr/bin/env python3
"""
Obsidian to Google Docs/Drive Sync Script
Synchronizes notes and media from Obsidian vault to Google Docs and Google Drive
"""

import os
import json
import argparse
from obsidian_sync import ObsidianToGoogleSync


def main():
    parser = argparse.ArgumentParser(
        description="Sync Obsidian notes to Google Docs/Drive. "
                   "Create 'allowed.list' file to sync only specific files.")
    parser.add_argument("--obsidian-path", default="./obsidian",
                       help="Path to Obsidian vault (default: ./obsidian)")
    parser.add_argument("--config", default="google_sync_config.json",
                       help="Configuration file path (default: google_sync_config.json)")
    parser.add_argument("--create-config", action="store_true",
                       help="Create a configuration file template")
    parser.add_argument("--credentials", default="credentials.json",
                       help="Google credentials file path (default: credentials.json)")
    parser.add_argument("--root-folder", default="Obsidian Sync",
                       help="Root folder name in Google Drive (default: Obsidian Sync)")
    parser.add_argument("--retry-failed", action="store_true",
                       help="Retry previously failed uploads from failed_uploads.json")

    args = parser.parse_args()

    if args.create_config:
        sync_tool = ObsidianToGoogleSync("", "")
        sync_tool.create_config_file(args.config)
        return

    # Load configuration
    if os.path.exists(args.config):
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)

        obsidian_path = config.get("obsidian_path", args.obsidian_path)
        credentials_path = config.get("credentials_path", args.credentials)
        root_folder_name = config.get("root_folder_name", args.root_folder)
    else:
        obsidian_path = args.obsidian_path
        credentials_path = args.credentials
        root_folder_name = args.root_folder

    if not os.path.exists(credentials_path):
        print(f"Google credentials file not found: {credentials_path}")
        print("Please download credentials.json from Google Cloud Console")
        print("Instructions:")
        print("1. Go to https://console.cloud.google.com/")
        print("2. Create a new project or select existing one")
        print("3. Enable Google Drive API and Google Docs API")
        print("4. Create credentials (OAuth 2.0 Client ID)")
        print("5. Download the JSON file and save as credentials.json")
        return

    # Initialize sync tool
    sync_tool = ObsidianToGoogleSync(obsidian_path, credentials_path)

    if args.retry_failed:
        # Retry failed uploads
        print("🔄 Retrying previously failed uploads...")
        success = sync_tool.retry_failed_uploads()

        if success:
            print("✅ Retry completed!")
        else:
            print("❌ Retry failed. Check the log for details.")
    else:
        # Run normal sync
        success = sync_tool.sync_notes(root_folder_name)

        if success:
            print("✅ Sync completed successfully!")
            print(f"Your notes have been uploaded to Google Drive in folder: {root_folder_name}")

            # Check if there were any failures
            if sync_tool.failed_uploads:
                print(f"⚠️  {len(sync_tool.failed_uploads)} files failed to process.")
                print("Check failed_uploads.json for details.")
                print("Run with --retry-failed to retry failed uploads.")
        else:
            print("❌ Sync failed. Check the log for details.")


if __name__ == "__main__":
    main()
