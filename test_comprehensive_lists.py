#!/usr/bin/env python3
"""
Comprehensive test suite for hierarchy lists functionality
"""

import sys
import os
from pathlib import Path

# Add the obsidian_sync directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'obsidian_sync'))

from obsidian_sync.markdown_processor import MarkdownProcessor
from obsidian_sync.file_manager import FileManager

class HierarchyListTester:
    """Comprehensive tester for hierarchy lists functionality"""
    
    def __init__(self):
        self.file_manager = FileManager("./obsidian")
        self.processor = MarkdownProcessor(self.file_manager)
        self.test_cases = self._create_test_cases()
    
    def _create_test_cases(self):
        """Create comprehensive test cases"""
        return {
            "basic_nesting": """
- Level 0
  - Level 1
    - Level 2
      - Level 3
        - Level 4
""",
            "mixed_types": """
1. Ordered root
   - Unordered nested
     1. Ordered deep
       - Unordered deeper
         1. Ordered deepest
2. Another ordered root
""",
            "tab_indentation": """
- Spaces item
\t- Tab item
\t\t- Double tab
\t\t\t- Triple tab
""",
            "mixed_indentation": """
- 2 spaces
    - 4 spaces  
        - 8 spaces
\t- Tab mixed with spaces
  - Back to 2 spaces
""",
            "continuation_lines": """
- Item with continuation
  that spans multiple lines
  with proper alignment
  - Nested with continuation
    that also spans lines
    and maintains structure
- Back to root
""",
            "empty_lines": """
- Item 1

- Item 2 after empty line
  - Nested after empty
  
  - Nested after double empty
  
- Back to root

- Final item
""",
            "complex_scenario": """
# Complex List Test

1. First ordered item
   - Nested unordered
     1. Deep ordered
       - Even deeper unordered
         1. Deepest ordered item
           with continuation lines
           that maintain proper structure
         2. Another deepest item
       - Back to deeper unordered
     2. Another deep ordered
   - Back to first level nested
2. Second ordered root
   
3. Third after empty line
   - More nested content
     - With deeper nesting
       - And even deeper
         - Maximum depth test
""",
            "edge_cases": """
- Normal item
-No space after dash (should not be list)
 - Space before dash
  - Two spaces before dash
   - Three spaces (odd indentation)
    - Four spaces
     - Five spaces
      - Six spaces

1. Ordered item
1.No space after dot
 1. Space before number
  2. Different number with spaces
   3. Three space indent
""",
            "numbering_patterns": """
1. Normal numbering
2. Sequential numbering
10. Higher numbers
100. Three digit numbers

1) Parenthesis style
2) Another parenthesis
10) Higher with parenthesis

a. Letter numbering (if supported)
b. Another letter
""",
            "special_characters": """
- Item with *bold* text
  - Item with **bold** and _italic_
    - Item with `code` inline
      - Item with [link](url)
        - Item with math $x^2$
- Back to root with **formatting**
"""
        }
    
    def run_all_tests(self):
        """Run all test cases and analyze results"""
        print("🧪 Running Comprehensive Hierarchy Lists Tests")
        print("=" * 60)
        
        total_tests = len(self.test_cases)
        passed_tests = 0
        results = {}
        
        for test_name, test_content in self.test_cases.items():
            print(f"\n📋 Testing: {test_name}")
            print("-" * 40)
            
            try:
                result = self._run_single_test(test_content)
                results[test_name] = result
                
                if result['success']:
                    print(f"✅ PASSED - {result['list_items']} list items, "
                          f"{result['max_nesting']} max nesting levels")
                    passed_tests += 1
                else:
                    print(f"❌ FAILED - {result['error']}")
                    
            except Exception as e:
                print(f"❌ EXCEPTION - {str(e)}")
                results[test_name] = {'success': False, 'error': str(e)}
        
        # Summary
        print(f"\n📊 Test Summary")
        print("=" * 60)
        print(f"Total tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success rate: {(passed_tests/total_tests)*100:.1f}%")
        
        # Detailed analysis
        self._analyze_results(results)
        
        return results
    
    def _run_single_test(self, content):
        """Run a single test case"""
        try:
            # Convert markdown to requests
            requests = self.processor.convert_markdown_to_docs_requests(
                content, Path("test.md")
            )
            
            # Analyze the requests
            analysis = self._analyze_requests(requests)
            
            return {
                'success': True,
                'requests': requests,
                'list_items': analysis['list_items'],
                'max_nesting': analysis['max_nesting'],
                'bullet_presets': analysis['bullet_presets'],
                'indent_levels': analysis['indent_levels']
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _analyze_requests(self, requests):
        """Analyze Google Docs requests for list information"""
        list_requests = [req for req in requests if 'createParagraphBullets' in req]
        indent_requests = [
            req for req in requests 
            if 'updateParagraphStyle' in req and 
               'indentFirstLine' in req.get('updateParagraphStyle', {}).get('paragraphStyle', {})
        ]
        
        # Count bullet presets
        bullet_presets = {}
        for req in list_requests:
            preset = req['createParagraphBullets']['bulletPreset']
            bullet_presets[preset] = bullet_presets.get(preset, 0) + 1
        
        # Count indent levels
        indent_levels = {}
        max_nesting = 0
        for req in indent_requests:
            magnitude = req['updateParagraphStyle']['paragraphStyle']['indentFirstLine']['magnitude']
            level = magnitude // 36  # 36 PT per level
            indent_levels[level] = indent_levels.get(level, 0) + 1
            max_nesting = max(max_nesting, level)
        
        return {
            'list_items': len(list_requests),
            'max_nesting': max_nesting,
            'bullet_presets': bullet_presets,
            'indent_levels': indent_levels
        }
    
    def _analyze_results(self, results):
        """Analyze overall test results"""
        print(f"\n🔍 Detailed Analysis")
        print("-" * 40)
        
        # Collect statistics
        total_list_items = 0
        max_nesting_overall = 0
        all_bullet_presets = set()
        
        for test_name, result in results.items():
            if result['success']:
                total_list_items += result['list_items']
                max_nesting_overall = max(max_nesting_overall, result['max_nesting'])
                all_bullet_presets.update(result['bullet_presets'].keys())
        
        print(f"Total list items processed: {total_list_items}")
        print(f"Maximum nesting level achieved: {max_nesting_overall}")
        print(f"Bullet presets used: {len(all_bullet_presets)}")
        print(f"Preset types: {', '.join(sorted(all_bullet_presets))}")
        
        # Show which tests had the most complex nesting
        complex_tests = [(name, result['max_nesting']) 
                        for name, result in results.items() 
                        if result['success']]
        complex_tests.sort(key=lambda x: x[1], reverse=True)
        
        print(f"\n📈 Most Complex Tests (by nesting level):")
        for name, nesting in complex_tests[:5]:
            print(f"  {name}: {nesting} levels")

def main():
    """Main test function"""
    tester = HierarchyListTester()
    results = tester.run_all_tests()
    return results

if __name__ == "__main__":
    main()
