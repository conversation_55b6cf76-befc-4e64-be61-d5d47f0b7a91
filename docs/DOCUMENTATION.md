# Obsidian to Google Drive Sync Tool - Complete Documentation

## Table of Contents

1. [Overview](#overview)
2. [New Modular Architecture](#new-modular-architecture)
3. [Installation & Setup](#installation--setup)
4. [Quick Start Guide](#quick-start-guide)
5. [Features & Capabilities](#features--capabilities)
6. [Migration Guide](#migration-guide)
7. [Testing & Quality Assurance](#testing--quality-assurance)
8. [Advanced Usage](#advanced-usage)
9. [Troubleshooting](#troubleshooting)
10. [API Reference](#api-reference)
11. [Contributing](#contributing)

---

## Overview

Công cụ Python để đồng bộ toàn bộ notes và media từ Obsidian vault sang Google Docs và Google Drive. Tool này đã được **tái cấu trúc thành modular architecture** để dễ maintain, test và mở rộng.

### 🎉 New in Latest Version

- **Modular Architecture**: Tách từ 1 file lớn thành multiple modules
- **Better Maintainability**: Dễ debug và maintain từng component
- **Enhanced Testing**: Unit test từng module riêng biệt
- **Improved Reusability**: <PERSON><PERSON> thể sử dụng lại components cho dự án khác
- **Backward Compatibility**: File cũ vẫn hoạt động bình thường

### Core Features

- ✅ Đồng bộ tất cả markdown files từ Obsidian
- ✅ Chuyển đổi Obsidian markdown syntax sang Google Docs format
- ✅ Hỗ trợ embedded images và media files với tự động chèn vào Google Docs
- ✅ Tự động tạo folder structure trên Google Drive dựa trên cấu trúc thư mục Obsidian
- ✅ Xử lý Obsidian links `[[link]]`
- ✅ Hỗ trợ headers, lists, tables, code blocks, và formatting
- ✅ Upload media files lên Google Drive
- ✅ **Smart file management**: Kiểm tra file đã tồn tại, replace nếu có, upload mới nếu chưa
- ✅ **Failed uploads tracking**: Lưu danh sách file upload fail để xử lý riêng
- ✅ **Selective sync với allowed.list**: Chỉ sync các file được chỉ định
- ✅ Logging chi tiết cho việc debug
- ✅ **Modular architecture**: Dễ maintain và extend

---

## New Modular Architecture

### Project Structure

```
obsidian_sync/           # 🆕 Main sync module
├── __init__.py         # Package initialization và exports
├── auth.py            # Google authentication management
├── drive_manager.py   # Google Drive operations (folders, files)
├── docs_manager.py    # Google Docs operations (documents, formatting)
├── markdown_processor.py # Markdown to Google Docs conversion
├── file_manager.py    # Obsidian vault file operations
├── sync.py           # Main synchronization coordinator
└── utils.py          # Utility functions (logging, text processing)

main.py               # 🆕 New entry point
obsidian_to_google_sync.py # Legacy file (still works)
```

### Module Responsibilities

#### `auth.py` - GoogleAuthManager

- OAuth2 authentication với Google APIs
- Token management và refresh
- Service object creation

#### `drive_manager.py` - GoogleDriveManager

- Folder creation và management
- File upload và update
- Media file handling
- Public URL generation

#### `docs_manager.py` - GoogleDocsManager

- Document creation và update
- Content formatting với Google Docs API
- Request validation và error handling
- Fallback methods cho complex errors

#### `markdown_processor.py` - MarkdownProcessor

- Obsidian markdown conversion
- Tables, lists, headers, code blocks processing
- Image reference handling
- Obsidian-specific syntax support

#### `file_manager.py` - FileManager

- Obsidian vault file discovery
- allowed.list filtering
- Media file location
- Folder structure organization

#### `sync.py` - ObsidianToGoogleSync

- Main coordination logic
- Error handling và retry logic
- Configuration management
- Progress tracking

### Benefits của Modular Architecture

1. **Separation of Concerns**: Mỗi module có trách nhiệm rõ ràng
2. **Maintainability**: Dễ maintain và debug từng phần
3. **Testability**: Có thể test từng component riêng biệt
4. **Reusability**: Có thể reuse managers cho dự án khác
5. **Scalability**: Dễ mở rộng thêm features mới
6. **Readability**: Code structure rõ ràng và dễ hiểu

---

## Installation & Setup

### System Requirements

- Python 3.7+
- Google Cloud Project với Google Drive API và Google Docs API được kích hoạt
- Google OAuth2 credentials
- Obsidian vault với markdown files

### Installation Steps

1. **Clone repository:**

```bash
git clone <repository-url>
cd obsidian-2-google-drive
```

2. **Install dependencies:**

```bash
pip3 install -r requirements.txt
```

3. **Verify installation:**

```bash
# Test new modular architecture
python3 -c "from obsidian_sync import ObsidianToGoogleSync; print('✅ Module import successful!')"

# Test entry point
python3 main.py --help
```

### Google Cloud Project Setup

#### Step 1: Tạo Google Cloud Project

1. Truy cập [Google Cloud Console](https://console.cloud.google.com/)
2. Tạo project mới hoặc chọn project hiện có
3. Kích hoạt các APIs cần thiết:
   - Google Drive API
   - Google Docs API

#### Step 2: Tạo OAuth2 Credentials

1. Trong Google Cloud Console, đi tới **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth 2.0 Client ID**
3. Chọn **Desktop application**
4. Download file JSON và lưu thành `credentials.json`

#### Step 3: Cấu hình quyền truy cập

Đảm bảo OAuth consent screen đã được cấu hình và thêm các scopes:

- `https://www.googleapis.com/auth/drive`
- `https://www.googleapis.com/auth/documents`

---

## Quick Start Guide

### Method 1: New Modular Architecture (Khuyến khích)

#### Step 1: Tạo config file

```bash
python3 main.py --create-config
```

#### Step 2: Cấu hình

Chỉnh sửa `google_sync_config.json`:

```json
{
  "obsidian_path": "./obsidian",
  "credentials_path": "credentials.json",
  "root_folder_name": "Obsidian Sync",
  "excluded_folders": [".obsidian", ".trash"],
  "excluded_files": ["*.tmp", "*.bak"]
}
```

#### Step 3: Chạy sync

```bash
# Basic sync
python3 main.py --obsidian-path ./obsidian --credentials credentials.json

# With custom config
python3 main.py --config my_config.json

# Retry failed uploads
python3 main.py --retry-failed
```

### Method 2: Legacy Method (Vẫn hoạt động)

```bash
# Tạo config
python3 obsidian_to_google_sync.py --create-config

# Chạy sync
python3 obsidian_to_google_sync.py

# Retry failed
python3 obsidian_to_google_sync.py --retry-failed
```

### Method 3: Programmatic Usage

```python
from obsidian_sync import ObsidianToGoogleSync

# Initialize
sync = ObsidianToGoogleSync("./obsidian", "credentials.json")

# Run sync
success = sync.sync_notes("My Obsidian Backup")

if success:
    print("✅ Sync completed!")

    # Check for failures
    if sync.failed_uploads:
        print(f"⚠️ {len(sync.failed_uploads)} files failed")
        print("Run retry_failed_uploads() to retry")
else:
    print("❌ Sync failed!")
```

### Makefile Integration

```bash
# Install dependencies
make install

# Create config for Google sync
make create-google-config

# Run Google sync (new method)
make sync-google-new

# Run Google sync (legacy method)
make sync-google

# Run OneNote sync
make sync-onenote
```

---

## Features & Capabilities

### Supported Obsidian Features

| Obsidian Markdown | Google Docs                                |
| ----------------- | ------------------------------------------ |
| `# Header 1`      | Heading 1 style                            |
| `## Header 2`     | Heading 2 style                            |
| `**bold**`        | Bold text                                  |
| `*italic*`        | Italic text                                |
| `[[link]]`        | Internal link (formatted text)             |
| `[[link\|alias]]` | Internal link with alias (formatted text)  |
| `[text](url)`     | External link (clickable with URL)         |
| `![[image.png]]`  | Uploaded image chèn trực tiếp vào document |
| Code blocks       | Monospace font với background              |
| Tables            | Text format (structured)                   |
| Lists             | Bullet/numbered lists với nested support   |

### 🔗 Link Detection & Conversion

Tool tự động phát hiện và chuyển đổi links trong Obsidian notes:

#### External Links

```markdown
[Google](https://www.google.com)
[GitHub](https://github.com)
[Example & Test](https://example.com/path?param=value&other=123)
```

**Conversion Process**:

1. Phát hiện pattern `[text](url)`
2. Tạo clickable link trong Google Docs với URL
3. Áp dụng formatting: màu xanh (#1565FF), underline
4. Hỗ trợ URLs có ký tự đặc biệt và parameters

#### Internal Obsidian Links

```markdown
[[My Note]] # Link đến note khác
[[Another Note|Custom Text]] # Link với alias
[[File with spaces & symbols|Display Name]]
```

**Conversion Process**:

1. Phát hiện pattern `[[target]]` và `[[target|alias]]`
2. Hiển thị alias (hoặc target nếu không có alias)
3. Áp dụng link formatting: màu xanh, underline
4. Ghi log để trace internal links

#### Mixed Content Support

```markdown
Tham khảo [[Programming Tips|tips]] và [Stack Overflow](https://stackoverflow.com) để học thêm.
```

Tool xử lý cả internal và external links trong cùng paragraph, đảm bảo formatting chính xác cho từng loại.

#### Technical Implementation

- **Preprocessing**: Links được phát hiện và thay thế bằng placeholders trước khi convert markdown
- **Formatting**: Áp dụng formatting trong quá trình tạo Google Docs requests
- **Error Handling**: Log chi tiết cho việc debug
- **Performance**: Xử lý song song multiple links trong cùng paragraph

### Image Processing Features

Module đã được cải tiến để xử lý hình ảnh một cách tự động:

1. **Tìm kiếm hình ảnh**: Tự động tìm hình ảnh trong các thư mục:

   - `media/`
   - `attachments/`
   - `assets/`

2. **Upload lên Google Drive**:

   - Tạo public URL cho embedding

3. **Chèn vào Google Docs**:

   - Tự động resize (400x300 pt)
   - Đúng vị trí trong nội dung

4. **Hỗ trợ format**:
   - PNG, JPG, JPEG, GIF, BMP, SVG
   - Audio files (MP3, WAV)

### Selective File Sync với allowed.list

Tool hỗ trợ selective sync sử dụng file `allowed.list`:

1. **Tạo file allowed.list** trong thư mục gốc của project:

```
# allowed.list - File filtering for Obsidian to Google Drive sync
# Add one filename per line (with or without .md extension)

My Important Note.md
Project Documentation
Meeting Notes 2024
```

2. **Quy tắc filtering**:

   - Nếu file `allowed.list` không tồn tại: sync tất cả files
   - Nếu file tồn tại nhưng rỗng (chỉ có comments): sync tất cả files
   - Nếu file có nội dung hợp lệ: chỉ sync các file được liệt kê
   - Hỗ trợ cả filename có và không có đuôi `.md`
   - Dòng bắt đầu bằng `#` được coi là comment và bỏ qua
   - Dòng trống được bỏ qua

3. **Logging**:
   - Log chi tiết về việc filter files
   - Thông báo số lượng files được tìm thấy và filtered

### Smart File Management

- **File existence check**: Kiểm tra file đã tồn tại trước khi upload
- **Replace vs Upload logic**: Update file hiện có thay vì tạo duplicate
- **Failed uploads tracking**: Lưu danh sách file fail để retry sau
- **Retry functionality**: Command line option để retry failed uploads

---

## Migration Guide

### Backward Compatibility

**Tin tốt**: File cũ `obsidian_to_google_sync.py` **vẫn hoạt động bình thường**! Bạn không cần thay đổi gì để tiếp tục sử dụng.

### Migration Path

#### Immediate (Không bắt buộc)

```bash
# Cách cũ vẫn hoạt động
python3 obsidian_to_google_sync.py
```

#### Recommended (Khi có thời gian)

```bash
# Chuyển sang cách mới
python3 main.py --obsidian-path ./obsidian
```

#### For Developers

```python
# Cũ:
from obsidian_to_google_sync import ObsidianToGoogleSync

# Mới (khuyến khích):
from obsidian_sync import ObsidianToGoogleSync
```

### Migration Benefits

1. **Better Error Handling**: Từng component có error handling riêng biệt
2. **Easier Debugging**: Debug từng module độc lập
3. **Unit Testing**: Test từng component riêng biệt
4. **Performance**: Load components on demand
5. **Extensibility**: Dễ thêm features mới

### Testing Migration

```bash
# Test cách cũ
python3 obsidian_to_google_sync.py --help

# Test cách mới
python3 main.py --help

# Test module import
python3 -c "from obsidian_sync import ObsidianToGoogleSync; print('✅ Success!')"
```

---

## Testing & Quality Assurance

### Comprehensive Test Suite

File `test_all_comprehensive.py` cung cấp testing toàn diện cho tất cả features:

#### Test Coverage

- ✅ **Grapheme Cluster Fix** - Xử lý ký tự tiếng Việt
- ✅ **Whitespace Cleanup Fix** - Loại bỏ whitespace-only lines
- ✅ **Nested Lists Fix** - Xử lý lists lồng nhau
- ✅ **Table Processing Fix** - Xử lý markdown tables
- ✅ **Image Placeholder Removal Fix** - Xử lý image references
- ✅ **Integration Tests** - Test toàn bộ workflow

#### Running Tests

```bash
# Basic test run
python3 test_all_comprehensive.py

# With full dependencies
pip install markdown beautifulsoup4 grapheme
python3 test_all_comprehensive.py
```

#### CI/CD Integration

```yaml
- name: Run comprehensive tests
  run: python3 test_all_comprehensive.py
```

#### Test Features

- **Mock implementations**: Hoạt động mà không cần Google API credentials
- **Vietnamese support**: Test comprehensive với text tiếng Việt
- **Comprehensive coverage**: Tất cả major fixes được test
- **Developer friendly**: Chi tiết logging và debug info

---

## Advanced Usage

### Command Line Options

```bash
# Sử dụng cơ bản
python3 obsidian_to_google_sync.py

# Retry failed uploads
python3 obsidian_to_google_sync.py --retry-failed

# Tạo config file mới
python3 obsidian_to_google_sync.py --create-config

# Sử dụng config file khác
python3 obsidian_to_google_sync.py --config "my_config.json"
```

#### Command Line Options (New Method)

```bash
# Basic sync
python3 main.py --obsidian-path ./obsidian --credentials credentials.json

# Custom root folder
python3 main.py --root-folder "My Obsidian Backup"

# Use config file
python3 main.py --config my_custom_config.json

# Create config template
python3 main.py --create-config

# Retry failed uploads
python3 main.py --retry-failed
```

### Configuration Options

```json
{
  "obsidian_path": "./obsidian",
  "credentials_path": "./credentials.json",
  "root_folder_name": "Obsidian Sync",
  "excluded_folders": [".obsidian", ".trash"],
  "excluded_files": ["*.tmp", "*.bak"],
  "max_retries": 3,
  "batch_size": 50
}
```

### Programmatic Usage với New Architecture

```python
from obsidian_sync import ObsidianToGoogleSync
from obsidian_sync.auth import GoogleAuthManager
from obsidian_sync.drive_manager import GoogleDriveManager

# Method 1: High-level API
sync = ObsidianToGoogleSync("./obsidian", "credentials.json")
success = sync.sync_notes("My Backup Folder")

# Method 2: Low-level component usage
auth = GoogleAuthManager("credentials.json")
if auth.authenticate():
    drive_manager = GoogleDriveManager(auth.get_drive_service())
    folder_id = drive_manager.find_or_create_folder("My Folder")
    # ... custom logic
```

### Makefile Integration (Updated)

```bash
# Install dependencies
make install

# Create configurations
make create-google-config
make create-onenote-config

# Run syncs (new methods)
make sync-google-new    # Uses main.py
make sync-google        # Uses legacy script
make sync-onenote       # OneNote sync

# Development
make test              # Run tests
make clean            # Clean temporary files
```

### Script Runners (Updated)

```bash
# Google sync runner (updated to use main.py)
./run_google_sync.sh

# Uses new main.py internally:
#!/bin/bash
python3 main.py --obsidian-path ./obsidian --credentials credentials.json "$@"
```

---

## Troubleshooting

### Common Issues

#### Authentication Errors

- Kiểm tra Client ID và credentials file
- Đảm bảo APIs được enable trong Google Cloud Console
- Thử delete `google_token.json` và authenticate lại

#### Module Import Errors

```bash
# Nếu gặp lỗi:
ModuleNotFoundError: No module named 'obsidian_sync'

# Giải pháp:
cd /path/to/project
python3 -c "import sys; print(sys.path)"  # Check PYTHONPATH
python3 main.py --help  # Use main.py instead

# Hoặc fallback về file cũ:
python3 obsidian_to_google_sync.py --help
```

#### Migration Issues

```bash
# Nếu có vấn đề với module mới, fallback về file cũ:
python3 obsidian_to_google_sync.py [your-options]

# Test cả hai methods:
python3 obsidian_to_google_sync.py --help  # Legacy
python3 main.py --help                     # New
```

#### Image Upload Failures

- Kiểm tra đường dẫn images trong markdown
- Đảm bảo images tồn tại trong vault
- Script tự động tìm trong `attachments/`, `assets/`, `media/`

#### Formatting Issues

- Check file encoding (should be UTF-8)
- Verify markdown syntax
- Check logs trong `sync.log`

#### Vietnamese Text Problems

- Đảm bảo đã cài `grapheme>=0.6.0`
- Check file encoding là UTF-8
- Test với file đơn giản trước

### Debugging

#### Enable Debug Logging

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### Check Failed Uploads

```bash
# Check failed uploads file
cat failed_uploads.json

# Retry failed uploads (new method)
python3 main.py --retry-failed

# Retry failed uploads (legacy method)
python3 obsidian_to_google_sync.py --retry-failed
```

#### Validate Requests

Script tự động validate requests trước khi gửi tới Google Docs API và log chi tiết errors.

---

## API Reference

### New Modular Architecture Classes

#### `ObsidianToGoogleSync` (Main Coordinator)

```python
from obsidian_sync import ObsidianToGoogleSync

class ObsidianToGoogleSync:
    def __init__(self, obsidian_path: str, credentials_path: str)
    def authenticate(self) -> bool
    def sync_notes(self, root_folder_name: str = "Obsidian Sync") -> bool
    def sync_single_note(self, note_path: Path, folder_id: str) -> bool
    def retry_failed_uploads(self) -> bool
    def create_config_file(self, config_path: str) -> None
```

#### `GoogleAuthManager`

```python
from obsidian_sync.auth import GoogleAuthManager

class GoogleAuthManager:
    def __init__(self, credentials_path: str)
    def authenticate(self) -> bool
    def get_drive_service(self)
    def get_docs_service(self)
```

#### `GoogleDriveManager`

```python
from obsidian_sync.drive_manager import GoogleDriveManager

class GoogleDriveManager:
    def __init__(self, drive_service)
    def create_folder(self, name: str, parent_id: str = None) -> Optional[str]
    def find_or_create_folder(self, name: str, parent_id: str = None) -> Optional[str]
    def upload_media_file(self, file_path: Path, folder_id: str) -> Optional[Tuple[str, str]]
    def find_existing_file(self, filename: str, folder_id: str) -> Optional[str]
```

#### `GoogleDocsManager`

```python
from obsidian_sync.docs_manager import GoogleDocsManager

class GoogleDocsManager:
    def __init__(self, docs_service, drive_service)
    def create_google_doc(self, title: str, content_requests: List[Dict], folder_id: str) -> Optional[str]
    def find_existing_doc(self, title: str, folder_id: str) -> Optional[str]
    def clear_document_content(self, doc_id: str) -> bool
    def insert_image_into_doc(self, doc_id: str, image_url: str, image_name: str, insert_index: int = 1) -> bool
```

#### `MarkdownProcessor`

```python
from obsidian_sync.markdown_processor import MarkdownProcessor

class MarkdownProcessor:
    def __init__(self, file_manager)
    def convert_markdown_to_docs_requests(self, markdown_content: str, note_path: Path) -> List[Dict]
    def process_images_in_markdown(self, markdown_content: str, note_path: Path) -> Tuple[str, List[Tuple[str, Path, str]]]
```

#### `FileManager`

```python
from obsidian_sync.file_manager import FileManager

class FileManager:
    def __init__(self, obsidian_path: str)
    def get_obsidian_notes(self) -> List[Path]
    def organize_notes_by_folder(self, notes: List[Path]) -> Dict[str, List[Path]]
    def read_allowed_list(self) -> Optional[List[str]]
    def find_media_file(self, base_path: Path, media_name: str) -> Optional[Path]
```

### Helper Functions

```python
from obsidian_sync.utils import grapheme_len, clean_whitespace_lines

def grapheme_len(text: str) -> int:
    """Calculate text length using grapheme clusters (Unicode-safe)"""

def clean_whitespace_lines(content: str) -> str:
    """Clean whitespace-only lines for better formatting"""
```

### Usage Examples

```python
# High-level usage
from obsidian_sync import ObsidianToGoogleSync

sync = ObsidianToGoogleSync("./obsidian", "credentials.json")
success = sync.sync_notes("My Backup")

# Low-level component usage
from obsidian_sync.auth import GoogleAuthManager
from obsidian_sync.drive_manager import GoogleDriveManager

auth = GoogleAuthManager("credentials.json")
if auth.authenticate():
    drive_manager = GoogleDriveManager(auth.get_drive_service())
    folder_id = drive_manager.find_or_create_folder("Test Folder")
    print(f"Created folder: {folder_id}")
```

### Legacy API (Still Supported)

```python
# Legacy import (still works)
from obsidian_to_google_sync import ObsidianToGoogleSync

def sync_vault_to_google_drive(self) -> None:
    """Main sync method"""

def convert_markdown_to_docs_requests(self, content: str, note_path: Path) -> List[Dict]:
    """Convert markdown to Google Docs API requests"""

def upload_media_file(self, file_path: Path, folder_id: str) -> Optional[str]:
    """Upload media file to Google Drive"""

def create_google_doc(self, title: str, requests: List[Dict], folder_id: str) -> Optional[str]:
    """Create Google Document with formatting"""
```

def find_media_file(image_name: str, base_path: Path) -> Optional[Path]:
"""Find media file in common directories"""

````

### Configuration Schema

```json
{
  "type": "object",
  "properties": {
    "obsidian_path": { "type": "string" },
    "credentials_path": { "type": "string" },
    "root_folder_name": { "type": "string" },
    "excluded_files": { "type": "array", "items": { "type": "string" } },
    "max_retries": { "type": "integer", "default": 3 },
    "batch_size": { "type": "integer", "default": 50 }
  },
  "required": ["obsidian_path"]
}
````

---

## Contributing

### Development Setup

1. Fork repository
2. Create virtual environment
3. Install dependencies: `pip install -r requirements.txt`
4. Run tests: `python3 test_all_comprehensive.py`

### Code Style

- Follow PEP 8
- Use type hints
- Add docstrings for public methods
- Include comprehensive tests

### Testing Requirements

- All new features must have tests
- Maintain 100% test pass rate
- Include edge cases
- Test with Vietnamese content

### Pull Request Process

1. Create feature branch
2. Implement changes với tests
3. Run comprehensive test suite
4. Update documentation
5. Submit PR with detailed description

---

## Project Structure

### New Modular Structure

```
obsidian-2-google-drive/
├── obsidian_sync/                   # 🆕 Main sync module
│   ├── __init__.py                 # Package initialization
│   ├── auth.py                     # Google authentication (~80 lines)
│   ├── drive_manager.py            # Google Drive operations (~150 lines)
│   ├── docs_manager.py             # Google Docs operations (~250 lines)
│   ├── markdown_processor.py       # Markdown conversion (~400 lines)
│   ├── file_manager.py             # File operations (~100 lines)
│   ├── sync.py                     # Main coordinator (~200 lines)
│   ├── utils.py                    # Utilities (~50 lines)
│   └── README.md                   # Module documentation
├── main.py                         # 🆕 New entry point (~80 lines)
├── obsidian_to_google_sync.py      # Legacy script (still works, 1900+ lines)
├── obsidian_to_onenote_sync.py     # OneNote sync script
├── test_all_comprehensive.py       # Comprehensive test suite
├── requirements.txt                # Python dependencies
├── google_sync_config.json         # Configuration file
├── Makefile                        # 🔄 Updated build automation
├── run_google_sync.sh             # 🔄 Updated script runner
├── docs/                          # Documentation
│   ├── DOCUMENTATION.md           # This file (updated)
│   └── index.md                   # Other docs
├── MIGRATION.md                   # 🆕 Migration guide
├── demo_image_sync.py            # Demo script
└── setup_example.py              # Setup example
```

### Benefits of New Structure

- **Modularity**: Each file has a single responsibility
- **Maintainability**: Easier to debug and maintain
- **Testability**: Can unit test each component separately
- **Reusability**: Components can be reused in other projects
- **Scalability**: Easy to add new features
- **Readability**: Cleaner code organization

---

## Version History

### v2.0.0 - Modular Architecture (Latest)

- ✅ **Refactored to modular architecture**
  - Split 1900+ line file into focused modules
  - Maintained 100% backward compatibility
  - Added new main.py entry point
- ✅ **Enhanced Developer Experience**
  - Better error handling per component
  - Easier debugging and testing
  - Cleaner API design
- ✅ **Improved Documentation**
  - Complete migration guide
  - Module-specific documentation
  - Updated examples and tutorials

### v1.x - Monolithic Architecture (Legacy)

- ✅ All major bug fixes implemented
- ✅ Comprehensive test suite
- ✅ Complete documentation
- ✅ Vietnamese text support
- ✅ Smart file management
- ✅ Production ready

### Recent Improvements

- Grapheme cluster handling
- Nested lists support
- Table formatting
- Image positioning
- Error recovery
- Test consolidation
- **Modular architecture refactoring**

---

## License

See `LICENSE` file for details.

---

## Support

For issues and questions:

1. Check this documentation
2. Run comprehensive tests
3. Check logs và failed_uploads.json
4. Create issue with detailed information

---

**🎉 Sync tool đã sẵn sàng cho production use!**

Công cụ này đã được test toàn diện và fix tất cả các issues được biết đến. Hỗ trợ đầy đủ cho content tiếng Việt và các tính năng nâng cao của Obsidian.
