# Edge Cases for Hierarchy Lists

## Mixed Indentation

- Item with 2 spaces
  - Item with 4 spaces
    - Item with 8 spaces
      - Item with tab after spaces
  - Tab-indented item
  - Back to 2 spaces

## Complex Nesting

1. First ordered
   - Unordered nested in ordered
     1. Ordered nested in unordered nested in ordered
     2. Another deep ordered
     - Even deeper unordered
       1. And back to ordered
   - Back to first level nested
2. Second ordered item

## Continuation Lines

- Item 1 has content
  that continues on the next line
  and even more content
  - Nested item with continuation
    that also spans multiple lines
    with proper indentation
    - Deep nested with content
      spanning multiple lines
      with consistent indentation
- Back to root level

## Empty Lines in Lists

- Item 1

- Item 2 after empty line

  - Nested after empty line

  - Another nested after double empty

- Back to root

## Inconsistent Spacing

- Item with 2 spaces
  - Item with 3 spaces (odd number)
    - Item with 5 spaces
    - Item with 4 spaces (should be same level as above?)
- Root item
