#!/usr/bin/env python3
"""
Enhanced Obsidian to Google Docs/Drive Sync Example
Demonstrates the new features including error handling, link management, and enhanced formatting
"""

import os
import sys
from pathlib import Path
from obsidian_sync import (
    ObsidianToGoogleSync, 
    ConfigManager, 
    SyncState, 
    <PERSON>rror<PERSON><PERSON><PERSON>,
    LinkManager
)


def main():
    """Main function demonstrating enhanced sync features"""
    
    # Configuration
    obsidian_path = "./obsidian"
    credentials_path = "credentials.json"
    config_file = "enhanced_sync_config.json"
    
    print("🚀 Enhanced Obsidian to Google Drive Sync")
    print("=" * 50)
    
    # Check if credentials exist
    if not os.path.exists(credentials_path):
        print(f"❌ Google credentials file not found: {credentials_path}")
        print("\nPlease follow these steps:")
        print("1. Go to https://console.cloud.google.com/")
        print("2. Create a new project or select existing one")
        print("3. Enable Google Drive API and Google Docs API")
        print("4. Create credentials (OAuth 2.0 Client ID)")
        print("5. Download the JSON file and save as credentials.json")
        return
    
    # Check if Obsidian vault exists
    if not os.path.exists(obsidian_path):
        print(f"❌ Obsidian vault not found: {obsidian_path}")
        print("Please specify the correct path to your Obsidian vault")
        return
    
    try:
        # Initialize configuration manager
        print("📋 Initializing configuration...")
        config_manager = ConfigManager(config_file)
        
        # Create default config if it doesn't exist
        if not Path(config_file).exists():
            config_manager.create_default_config_file(config_file)
            print(f"✅ Created default configuration: {config_file}")
        
        # Display current configuration
        print("\n📊 Current Configuration:")
        print(f"  - Max retry attempts: {config_manager.get_retry_config().max_attempts}")
        print(f"  - Create missing targets: {config_manager.get_link_config().create_missing_targets}")
        print(f"  - Enhanced formatting: {config_manager.get_formatting_config().enable_bold}")
        print(f"  - Batch size: {config_manager.get_sync_config().batch_size}")
        
        # Initialize sync tool with enhanced features
        print("\n🔧 Initializing sync tool...")
        sync_tool = ObsidianToGoogleSync(obsidian_path, credentials_path, config_file)
        
        # Check if we can resume a previous sync
        if sync_tool.sync_state.can_resume():
            print("\n🔄 Previous sync session found!")
            progress = sync_tool.sync_state.get_progress_summary()
            print(f"  - Total items: {progress['total_items']}")
            print(f"  - Completed: {progress['completed']}")
            print(f"  - Failed: {progress['failed']}")
            print(f"  - Pending: {progress['pending']}")
            
            resume = input("\nDo you want to resume the previous sync? (y/n): ").lower().strip()
            if resume == 'y':
                print("🔄 Resuming previous sync...")
                # Implementation for resuming would go here
                # For now, we'll start a new sync
        
        # Start sync process
        print("\n🚀 Starting sync process...")
        root_folder_name = "Enhanced Obsidian Sync"
        
        success = sync_tool.sync_notes(root_folder_name)
        
        if success:
            print("\n✅ Sync completed successfully!")
            
            # Display detailed results
            progress = sync_tool.sync_state.get_progress_summary()
            print(f"\n📊 Sync Results:")
            print(f"  - Total notes: {progress['total_items']}")
            print(f"  - Successfully synced: {progress['completed']}")
            print(f"  - Failed: {progress['failed']}")
            print(f"  - Skipped: {progress['skipped']}")
            print(f"  - Completion: {progress['completion_percentage']:.1f}%")
            
            # Display link statistics if available
            if sync_tool.link_manager:
                link_stats = sync_tool.link_manager.get_link_statistics()
                print(f"\n🔗 Link Statistics:")
                print(f"  - Notes with links: {link_stats['total_notes_with_links']}")
                print(f"  - Total internal links: {link_stats['total_internal_links']}")
                print(f"  - Missing targets created: {link_stats['missing_targets']}")
                print(f"  - Mapped documents: {link_stats['mapped_docs']}")
            
            # Display error statistics
            error_report = sync_tool.error_handler.create_error_report()
            error_stats = error_report['error_statistics']
            print(f"\n⚠️  Error Statistics:")
            print(f"  - Total errors: {error_stats['total_errors']}")
            print(f"  - Retries attempted: {error_stats['retries_attempted']}")
            print(f"  - Circuit breaker trips: {error_stats['circuit_breaker_trips']}")
            
            if error_stats['errors_by_type']:
                print("  - Errors by type:")
                for error_type, count in error_stats['errors_by_type'].items():
                    print(f"    * {error_type}: {count}")
            
            # Check for failed items
            failed_items = sync_tool.sync_state.get_failed_items()
            if failed_items:
                print(f"\n❌ {len(failed_items)} items failed to sync:")
                for item in failed_items[:5]:  # Show first 5 failed items
                    print(f"  - {Path(item.file_path).name}: {item.error}")
                
                if len(failed_items) > 5:
                    print(f"  ... and {len(failed_items) - 5} more")
                
                print("\n💡 You can retry failed items later using the retry functionality")
            
            print(f"\n🎉 Your notes are now available in Google Drive folder: '{root_folder_name}'")
            
        else:
            print("\n❌ Sync failed!")
            
            # Display error information
            failed_items = sync_tool.sync_state.get_failed_items()
            if failed_items:
                print(f"Failed items: {len(failed_items)}")
                for item in failed_items[:3]:
                    print(f"  - {Path(item.file_path).name}: {item.error}")
            
            print("Check the logs for more details.")
    
    except KeyboardInterrupt:
        print("\n\n⏹️  Sync interrupted by user")
        print("Progress has been saved and can be resumed later")
    
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("Check the logs for more details")
    
    finally:
        print("\n📝 Check the following files for more information:")
        print("  - google_sync.log: Detailed sync logs")
        print("  - sync_state.json: Sync progress and state")
        print("  - link_registry.json: Internal link mappings")
        print("  - enhanced_sync_config.json: Configuration settings")


def show_help():
    """Show help information"""
    print("Enhanced Obsidian to Google Drive Sync")
    print("=" * 40)
    print("\nFeatures:")
    print("✅ Robust error handling with automatic retries")
    print("✅ Internal link validation and missing target creation")
    print("✅ Enhanced formatting (bold, italic, code, strikethrough)")
    print("✅ Improved code block processing with syntax detection")
    print("✅ Progress tracking and resumable operations")
    print("✅ Comprehensive configuration management")
    print("✅ Circuit breaker pattern for API protection")
    print("\nUsage:")
    print("  python example_enhanced_sync.py")
    print("\nConfiguration:")
    print("  Edit enhanced_sync_config.json to customize behavior")
    print("\nFor more information, see the documentation.")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_help()
    else:
        main()
