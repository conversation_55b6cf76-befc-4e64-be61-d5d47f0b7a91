# Hierarchy Lists Support - Technical Documentation

## Overview

This document describes the enhanced hierarchy lists support implemented in the Obsidian to Google Drive sync tool. The implementation follows best practices and provides comprehensive support for nested lists with proper Google Docs formatting.

## Features

### ✅ Supported List Features

1. **Multi-level Nesting**: Supports up to unlimited nesting levels
2. **Mixed List Types**: Ordered and unordered lists can be nested within each other
3. **Flexible Indentation**: Supports both spaces and tabs for indentation
4. **Continuation Lines**: Multi-line list items are properly handled
5. **Different Bullet Styles**: Different bullet/numbering styles per nesting level
6. **Proper Google Docs Formatting**: Uses Google Docs native list formatting

### 📋 List Types Supported

#### Unordered Lists

- `- Item` (hyphen)
- `* Item` (asterisk)
- `+ Item` (plus)

#### Ordered Lists

- `1. Item` (numbers with dot)
- `1) Item` (numbers with parenthesis)

### 🎯 Indentation Rules

- **2 spaces** = 1 nesting level
- **4 spaces** = 2 nesting levels
- **1 tab** = 1 nesting level (treated as 4 spaces)
- Mixed indentation is normalized to consistent levels

## Implementation Architecture

### Core Components

1. **`HierarchyListProcessor`**: Enhanced processor with best practices
2. **`ListItem`** dataclass: Structured representation of list items
3. **`ListType`** enum: Type-safe list type definitions

### Best Practices Applied

1. **Separation of Concerns**: Dedicated processor for hierarchy lists
2. **Type Safety**: Using enums and dataclasses for better code reliability
3. **Error Handling**: Graceful fallback to basic processing
4. **Constants**: Configurable constants for maintainability
5. **Clean Architecture**: Modular design with clear interfaces

## Google Docs Formatting

### Bullet Presets by Level

#### Unordered Lists

- **Level 0**: `BULLET_DISC_CIRCLE_SQUARE`
- **Level 1**: `BULLET_DIAMONDX_ARROW3D_SQUARE`
- **Level 2+**: `BULLET_CHECKBOX`

#### Ordered Lists

- **Level 0**: `NUMBERED_DECIMAL_ALPHA_ROMAN`
- **Level 1**: `NUMBERED_DECIMAL_ALPHA_ROMAN_PARENS`
- **Level 2+**: `NUMBERED_DECIMAL_NESTED`

### Indentation

- **36 points per nesting level** for optimal spacing
- Uses both `indentFirstLine` and `indentStart` for proper alignment

## Usage Examples

### Basic Nested Lists

```markdown
- Item 1
  - Nested item 1.1
  - Nested item 1.2
    - Deep nested 1.2.1
- Item 2
```

### Mixed List Types

```markdown
1. Ordered item
   - Unordered nested
   - Another unordered
     1. Back to ordered
     2. Another ordered
2. Another ordered item
```

### Complex Indentation

```markdown
- Item with 2 spaces
  - Sub-item with 2 spaces
    - Sub-sub-item with 2 spaces
      - Even deeper nesting
- Another item
  - Item with 4 spaces (treated as nested)
    - Item with 8 spaces (deeper nesting)
```

## API Reference

### HierarchyListProcessor

#### Methods

- `process_hierarchy_lists(lines, start_index)`: Main processing method
- `_parse_list_hierarchy(lines)`: Parse lines into structured format
- `_calculate_indent_level(line)`: Calculate indentation with tab support
- `_get_bullet_preset(is_ordered, nesting_level)`: Get appropriate bullet style

#### Configuration

```python
class HierarchyListProcessor:
    INDENT_PER_LEVEL_PT = 36  # Points per nesting level
    TAB_SIZE = 4  # Treat tab as 4 spaces

    UNORDERED_PRESETS = [
        'BULLET_DISC_CIRCLE_SQUARE',
        'BULLET_DIAMONDX_ARROW3D_SQUARE',
        'BULLET_CHECKBOX'
    ]

    ORDERED_PRESETS = [
        'NUMBERED_DECIMAL_ALPHA_ROMAN',
        'NUMBERED_DECIMAL_ALPHA_ROMAN_PARENS',
        'NUMBERED_DECIMAL_NESTED'
    ]
```

## Testing

### Test Coverage

The implementation includes comprehensive testing for:

1. **Basic nested lists**: Simple hierarchy scenarios
2. **Edge cases**: Complex indentation, mixed types, empty lines
3. **Error scenarios**: Malformed lists, inconsistent indentation

### Test Results

Based on current test results:

- ✅ **4+ nesting levels** properly handled
- ✅ **Mixed list types** working correctly
- ✅ **Different bullet presets** applied per level
- ✅ **Complex indentation scenarios** handled properly

## Migration Guide

### For Existing Code

The enhanced hierarchy processor is backward compatible:

1. **Automatic fallback**: If enhanced processor fails, falls back to basic processing
2. **Same interface**: No changes required to calling code
3. **Improved output**: Better formatting with same input

### Configuration

No configuration changes are required. The enhanced processor uses sensible defaults that work well with Google Docs.

## Performance Considerations

1. **Efficient parsing**: Single-pass parsing with stack-based hierarchy tracking
2. **Memory usage**: Minimal overhead with structured data classes
3. **Error resilience**: Graceful handling of malformed input

## Future Enhancements

Potential areas for future improvement:

1. **Custom bullet styles configuration**
2. **Advanced continuation line handling**
3. **List style inheritance**
4. **Performance optimizations for large lists**

## Troubleshooting

### Common Issues

1. **Inconsistent indentation**: Use consistent spaces/tabs
2. **Missing spaces after bullets**: Ensure `- Item` not `-Item`
3. **Mixed indentation**: Stick to either spaces or tabs

### Debug Information

Enable debug logging to see detailed list processing information:

```python
import logging
logging.getLogger('obsidian_sync').setLevel(logging.DEBUG)
```
